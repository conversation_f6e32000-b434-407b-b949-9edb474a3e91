<?php
require  ROOT_PATH . '/class/Twilio/autoload.php';

use Twilio\Rest\Client;

function converToTz($time = "")
{
	$fromTz = 'America/New_York';
	$toTz = 'Asia/Calcutta';

	// timezone by php friendly values
	$date = new DateTime($time, new DateTimeZone($fromTz));
	$date->setTimezone(new DateTimeZone($toTz));
	$time = $date->format('Y-m-d H:i:s');
	return $time;
}

function cleanString($string)
{
	$string = str_replace(' ', '_', $string); // Replaces all spaces with hyphens.

	return preg_replace('/[^A-Za-z0-9\-]/', '', $string); // Removes special chars.
}

function GenerateOrderLimitString($pageno, $recordsPerPages = 0)
{
	$recordsPerPage = 10;
	$returnString = "";
	if ($pageno > 0) {
		if ($recordsPerPages)
			$recordsPerPage = $recordsPerPages;

		$limitStart = 0;
		$limitTo = $recordsPerPage;

		$pageno = $pageno - 1;

		$limitStart = $pageno * $recordsPerPage;

		$returnString = " limit " . $limitStart . ', ' . $limitTo;
	} else {
		$returnString = "";
	}
	return $returnString;
}

function GenerateLoadMoreLimitString($pageno)
{

	$returnString = "";
	if ($pageno > 0) {
		$limitStart = $pageno * 9;
		$returnString = " limit 0, " . $limitStart;
	} else {
		$returnString = "";
	}
	return $returnString;
}

function detect_mobile()
{
	if (preg_match('/(alcatel|amoi|android|avantgo|blackberry|benq|cell|cricket|docomo|elaine|htc|iemobile|iphone|ipad|ipaq|ipod|j2me|java|midp|mini|mmp|mobi|motorola|nec-|nokia|palm|panasonic|philips|phone|playbook|sagem|sharp|sie-|silk|smartphone|sony|symbian|t-mobile|telus|up\.browser|up\.link|vodafone|wap|webos|wireless|xda|xoom|zte)/i', $_SERVER['HTTP_USER_AGENT']))
		return true;

	else
		return false;
}

function getFileExtensionFromBase64($base64String)
{
	// Regular expression to extract the MIME type
	$pattern = '/^data:(.*?);base64/';
	$matches = [];

	preg_match($pattern, $base64String, $matches);

	if (!isset($matches[1])) {
		throw new Exception('Invalid base64 string: MIME type not found');
	}

	$mimeType = $matches[1];

	// Map MIME type to file extension
	$mimeToExtension = [
		'image/jpeg' => 'jpg',
		'image/png' => 'png',
		'image/gif' => 'gif',
		'image/bmp' => 'bmp',
		'image/webp' => 'webp',
		'image/svg+xml' => 'svg',
		// Add more MIME types and extensions as needed
	];

	if (!array_key_exists($mimeType, $mimeToExtension)) {
		throw new Exception("Unsupported MIME type: $mimeType");
	}

	return $mimeToExtension[$mimeType];
}

function createPageSlug($string)
{
	$slug = preg_replace('/[^A-Za-z0-9-]+/', '-', $string);
	$slug = strtolower($slug);
	return $slug;
}

function delete_directory($dirname)
{
	$dir_handle = "";
	if (is_dir($dirname))
		$dir_handle = opendir($dirname);
	if (!$dir_handle)
		return false;
	while ($file = readdir($dir_handle)) {
		if ($file != "." && $file != "..") {
			if (!is_dir($dirname . "/" . $file))
				unlink($dirname . "/" . $file);
			else
				delete_directory($dirname . '/' . $file);
		}
	}
	closedir($dir_handle);
	@rmdir($dirname);
	return true;
}

function copy_directory($source, $target)
{
	if (is_dir($source)) {
		@mkdir($target);
		$d = dir($source);
		while (FALSE !== ($entry = $d->read())) {
			if ($entry == '.' || $entry == '..') {
				continue;
			}
			$Entry = $source . '/' . $entry;
			if (is_dir($Entry)) {
				copy_directory($Entry, $target . '/' . $entry);
				continue;
			}
			copy($Entry, $target . '/' . $entry);
		}

		$d->close();
	} else {
		copy($source, $target);
	}
}



function GetCurrentDomainNameFromURL()
{
	$url = "http://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
	return parse_url($url, PHP_URL_HOST);
}

function create_slug($string)
{
	$slug = preg_replace('/[^A-Za-z0-9-]+/', '-', $string);
	return strtolower($slug);
}

function create_school_code($schoolId)
{
	return sprintf('%04d', $schoolId);
}


function GenerateRandomAlphaNumericNumber($digits = 6)
{
	$key = '';
	$keys = array_merge(range(0, 9), range('a', 'z'));

	for ($i = 0; $i < $digits; $i++) {
		$key .= $keys[array_rand($keys)];
	}

	return $key;
}

function array_sort_by_column(&$array, $column, $direction = SORT_ASC)
{
	$reference_array = array();

	foreach ($array as $key => $row) {
		$reference_array[$key] = $row[$column];
	}

	array_multisort($reference_array, $direction, $array);
}

function GetUserImagePath($userId, $schoolId, $profilePic)
{
	$defaultImagePath = BASE_PATH . "/upload/default-user.png";
	if ($profilePic != "") {
		$checkImagePath = ROOT_PATH . "/upload/schools/" . $schoolId . "/users/" . $userId . "/" . $profilePic;

		if (file_exists($checkImagePath)) {
			$defaultImagePath =   BASE_PATH . "/upload/schools/" . $schoolId . "/users/" . $userId . "/" . $profilePic;
		}
	}
	return $defaultImagePath;
}


function GetClinicianImagePath($userId, $schoolId, $defaultImageName)
{
	$defaultImagePath = BASE_PATH . "/upload/default-user.png";
	if ($defaultImageName != "") {
		$checkImagePath = ROOT_PATH . "/upload/schools/" . $schoolId . "/clinician/" . $userId . "/" . $defaultImageName;
		if (file_exists($checkImagePath)) {
			$defaultImagePath =   BASE_PATH . "/upload/schools/" . $schoolId . "/clinician/" . $userId . "/" . $defaultImageName;
		}
	}
	return $defaultImagePath;
}

function GetStudentDocumentPath($schoolId, $studentId, $documentId, $defaultImageName)
{
	$defaultDocPath = '';
	if ($defaultImageName != "") {
		$defaultDocPath = ROOT_PATH . "/upload/schools/" . $schoolId . "/student/" . $studentId . "/documents/" . $defaultImageName;
		if (file_exists($defaultDocPath)) {
			$defaultDocPath =   BASE_PATH . "/upload/schools/" . $schoolId . "/student/" . $studentId . "/documents/" . $defaultImageName;
		}
	}
	return $defaultDocPath;
}

function GetClinicianDocumentPath($schoolId, $clinicianId, $documentId, $defaultImageName)
{
	$defaultDocPath = '';
	if ($defaultImageName != "") {
		$defaultDocPath = ROOT_PATH . "/upload/schools/" . $schoolId . "/Clinician/" . $clinicianId . "/documents/" . $defaultImageName;
		if (file_exists($defaultDocPath)) {
			$defaultDocPath =   BASE_PATH . "/upload/schools/" . $schoolId . "/Clinician/" . $clinicianId . "/documents/" . $defaultImageName;
		}
	}
	return $defaultDocPath;
}


function GetStudentImagePath($Id, $schoolId, $defaultImageName)
{
	$defaultImagePath = BASE_PATH . "/upload/default-user.png";
	if ($defaultImageName != "") {
		$checkImagePath = ROOT_PATH . "/upload/schools/" . $schoolId . "/student/" . $Id . "/" . $defaultImageName;

		if (file_exists($checkImagePath)) {
			$defaultImagePath =   BASE_PATH . "/upload/schools/" . $schoolId . "/student/" . $Id . "/" . $defaultImageName;
		}
	}
	return $defaultImagePath;
}


function GetSchoolImagePath($schoolId, $defaultImageName)
{
	$defaultImagePath = BASE_PATH . "/upload/schools/default-school.png";

	if ($defaultImageName != "") {
		$checkImagePath = ROOT_PATH . "/upload/schools/" . $schoolId . "/logo/" . $defaultImageName;

		if (file_exists($checkImagePath)) {
			$defaultImagePath = BASE_PATH . "/upload/schools/" . $schoolId . "/logo/" . $defaultImageName;
		}
	}

	return $defaultImagePath;
}


function xml2array($xmlObject, $out = array())
{
	foreach ((array) $xmlObject as $index => $node)
		$out[$index] = (is_object($node)) ? xml2array($node) : $node;

	return $out;
}

function GetDateStringInServerFormat($inputDate)
{
	$returnDate = date('Y-m-d H:i:s');
	if ($inputDate != "") {
		//$inputDate = date('Y-m-d H:i:s',strtotime($inputDate));
		$courseStartDateTimetring = strtotime($inputDate);
		$returnDate = date('Y-m-d H:i:s', $courseStartDateTimetring);
	}
	return $returnDate;
}

// function GetDateStringInServerFormat($inputDate)
// {
//     $returnDate = date('Y-m-d H:i:s');

//     if (!empty($inputDate)) {
//         // Detect MM/DD/YYYY format and convert it to YYYY-MM-DD
//         if (preg_match('/^\d{2}\/\d{2}\/\d{4}$/', $inputDate)) {
//             $inputDate = DateTime::createFromFormat('m/d/Y', $inputDate)->format('Y-m-d');
//         }

//         // Check if input is only a date without a time
//         if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $inputDate)) {
//             $inputDate .= ' 23:59:59'; // Append time to prevent previous day shift
//         }

//         $courseStartDateTimetring = strtotime($inputDate);
//         $returnDate = date('Y-m-d H:i:s', $courseStartDateTimetring);
//     }

//     return $returnDate;
// }


function downloadFile($file_url)
{
	$downloadFileName = basename($file_url);
	header("Content-Type: application/octet-stream");
	header("Content-Transfer-Encoding: Binary");
	header("Content-disposition: attachment; filename=" . $downloadFileName . "");
	echo readfile($file_url);
	exit();
}

function EncodeQueryData($input)
{
	return base64_encode($input);
}

function DecodeQueryData($input)
{
	return base64_decode($input);
}

function GetSingleFieldArrayFromResultSet($result, $coulmname)
{
	$results_array = array();
	while ($row = mysqli_fetch_assoc($result)) {
		$results_array[] = $row[$coulmname];
	}
	return $results_array;
}

function resultSetToArray($resultset)
{
	$results = array();
	while ($row = mysqli_fetch_assoc($resultset)) {
		$results[] = $row;
	}
	return $results;
}

function generate_password()
{
	$returnPassword = '';
	$digits = 3;

	//Special Chars
	$specialChars = array('!', '^', '&', ';', ':', ',', '.', '|', '~', '`', '#', '$', '%', '(', ')', '[', ']', '{', '}', '*', '+', '-', '=', '-', '/', '<', '>', '?');
	$specialCharsCount = count($specialChars);

	//5 Small Letters Capital Letter a-z
	for ($i = 0; $i < $digits; $i++) {
		$returnPassword .= chr(rand(97, 122));
	}

	//One Capital Letter A-Z
	$returnPassword .= chr(rand(65, 90));

	//One Symbol Value
	$returnPassword .= $specialChars[rand(0, $specialCharsCount - 1)];

	//One Number Value = 0-9
	$returnPassword .= chr(rand(48, 57));

	return $returnPassword;
}
function GetSummativeQuestionHtml($questionId, $type, $studentSummativeMasterId, $schoolId)
{
	$objQuestionOption = new clsQuestionOption();
	$getQuestionOptions = $objQuestionOption->GetQuestionOptionsOfsummative($questionId, $studentSummativeMasterId);
	$getTextAns = $objQuestionOption->GetTextAnsOfsummative($questionId, $studentSummativeMasterId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}

function GetFormativeQuestionHtml($questionId, $type, $studentFormativeMasterId, $schoolId)
{
	$objQuestionOption = new clsQuestionOption();
	$getQuestionOptions = $objQuestionOption->GetFormativeQuestionOptions($questionId, $studentFormativeMasterId);
	$getTextAns = $objQuestionOption->GetTextAnsOfFormativeEval($questionId, $studentFormativeMasterId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}

function GetPEFQuestionHtml($questionId, $type, $studentPEFMasterId, $schoolId)
{
	$objQuestionOption = new clsQuestionOption();
	$getQuestionOptions = $objQuestionOption->GetPEFQuestionOptions($questionId, $studentPEFMasterId);
	$getTextAns = $objQuestionOption->GetTextAnsOfPEFEval($questionId, $studentPEFMasterId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}

function GetMidtermQuestionHtml($questionId, $type, $studentMidtermMasterId, $schoolId)
{
	$objQuestionOption = new clsQuestionOption();
	$getQuestionOptions = $objQuestionOption->GetMidtermQuestionOptions($questionId, $studentMidtermMasterId);
	$getTextAns = $objQuestionOption->GetTextAnsOfMidtermEval($questionId, $studentMidtermMasterId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}
function GetDailyQuestionHtml($questionId, $type, $studentDailyMasterId, $schoolId)
{
	$getTextAns = '';
	$objQuestionOption = new clsQuestionOption();
	$getQuestionOptions = $objQuestionOption->GetDailyQuestionOptions($questionId, $studentDailyMasterId);
	$getTextAns = $objQuestionOption->GetTextAnsOfDailyEval($questionId, $studentDailyMasterId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}

function GetCIEvaluationQuestionHtml($questionId, $type, $ciEvaluationMasterId, $schoolId)
{
	$objQuestionOption = new clsQuestionOption();
	$getQuestionOptions = $objQuestionOption->GetQuestionOptionsOfCIEvaluation($questionId, $ciEvaluationMasterId);
	$getTextAns = $objQuestionOption->GetTextAnsOfCIEvaluation($questionId, $ciEvaluationMasterId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}

function GetPEvaluationQuestionHtml($questionId, $type, $pEvaluationMasterId, $schoolId)
{
	$objQuestionOption = new clsQuestionOption();
	$getQuestionOptions = $objQuestionOption->GetQuestionOptionsOfPEvaluation($questionId, $pEvaluationMasterId);
	$getTextAns = $objQuestionOption->GetTextAnsOfPEvaluation($questionId, $pEvaluationMasterId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}

function GetFloorTherapyAndICUEvaluationQuestionHtml($questionId, $type, $evaluationMasterId, $schoolId)
{
	$objQuestionOption = new clsQuestionOption();
	$getQuestionOptions = $objQuestionOption->GetQuestionOptionsOfFloorTherapyAndICUEvaluation($questionId, $evaluationMasterId);
	$getTextAns = $objQuestionOption->GetTextAnsOfFloorTherapyAndICUEvaluation($questionId, $evaluationMasterId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}


function GetCSEvaluationQuestionHtml($questionId, $type, $csEvaluationMasterId, $schoolId)
{
	$objQuestionOption = new clsQuestionOption();
	$getQuestionOptions = $objQuestionOption->GetQuestionOptionsOfCSEvaluation($questionId, $csEvaluationMasterId);
	$getTextAns = $objQuestionOption->GetTextAnsOfCSEvaluation($questionId, $csEvaluationMasterId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}

function GetCoarcQuestionHtml($questionId, $type, $studentCoarcMasterId, $schoolId)
{
	$objQuestionOption = new clsQuestionOption();
	$getTextAns = 0;
	$getQuestionOptions = $objQuestionOption->GetQuestionOptionsOfCoarc($questionId, $studentCoarcMasterId);
	$getTextAns = $objQuestionOption->GetTextAnsOfStudentCoarc($questionId, $studentCoarcMasterId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}

function GetGraduateCoarcQuestionHtml($questionId, $type, $studentCoarcMasterId, $schoolId)
{
	$objQuestionOption = new clsQuestionOption();
	$getTextAns = 0;
	$getQuestionOptions = $objQuestionOption->GetQuestionOptionsOfGraduateCoarc($questionId, $studentCoarcMasterId);
	$getTextAns = $objQuestionOption->GetTextAnsOfGraduateCoarc($questionId, $studentCoarcMasterId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}
function GetEmployerCoarcQuestionHtml($questionId, $type, $employerCoarcMasterId, $schoolId)
{
	$objQuestionOption = new clsQuestionOption();
	$getTextAns = 0;
	$getQuestionOptions = $objQuestionOption->GetQuestionOptionsOfEmployerCoarc($questionId, $employerCoarcMasterId);
	$getTextAns = $objQuestionOption->GetTextAnsOfEmployerCoarc($questionId, $employerCoarcMasterId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}
function GetPersonnelCoarcQuestionHtml($questionId, $type, $personnelCoarcMasterId, $schoolId, $masterQuestionId = 0)
{
	$objQuestionOption = new clsQuestionOption();
	$getTextAns = 0;
	$getQuestionOptions = $objQuestionOption->GetQuestionOptionsOfPersonnelCoarc($questionId, $personnelCoarcMasterId);
	$getTextAns = $objQuestionOption->GetTextAnsOfPersonnelCoarc($questionId, $personnelCoarcMasterId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId, $masterQuestionId);
}


function RenderQuestionChoicesNewHtml($getQuestionOptions, $type, $questionId, $getTextAns, $ID, $schoolId, $masterQuestionId = 0)
{


	// echo $type; exit;
	$qhtml = '';

	//Get Coarc Section Ids
	include_once('../class/clsStudentCoarcMaster.php');
	$StudentCoarcMaster = new clsStudentCoarcMaster();
	$sectionIds = $StudentCoarcMaster->GetCoarcSectionIdId($questionId);
	// print_r($sectionIds);exit;
	$studentCoarcSectionId = $sectionIds['studentCoarcSectionId'] ? $sectionIds['studentCoarcSectionId'] : 0;
	$employerCoarcSectionId = $sectionIds['employerCoarcSectionId'] ? $sectionIds['employerCoarcSectionId'] : 0;
	$graduateCoarcSectionId = $sectionIds['graduateCoarcSectionId'] ? $sectionIds['graduateCoarcSectionId'] : 0;
	$personnelCoarcSectionId = $sectionIds['personnelCoarcSectionId'] ? $sectionIds['personnelCoarcSectionId'] : 0;

	$isPositionFormative = '';
	$isPositionCi = '';
	$isPositionSummative = '';
	$isPositionSite = '';
	$isPositionMidterm = '';

	//Display TextArea 
	if ($type == 5) {
		if ($getTextAns == 'Checkoff') {
			$objQuestionOption = new clsQuestionOption();
			$getTextAns = $objQuestionOption->GetTextAnsOfCheckoff($questionId, $ID, $schoolId);
			//unset($objQuestionOption);
		}

		$totalTextCount = mysqli_num_rows($getTextAns);
		if ($totalTextCount > 0) {
			while ($rows = mysqli_fetch_array($getTextAns)) {
				$TextAnswer = '';
				$TextAnswer = $rows["TextAnswer"];
				$qhtml .= '<textarea name="questionoptionst_' .  ($questionId) . '_' . ($masterQuestionId) . '[]" id="textarea" class="form-control input-md clstextarea" rows="4" cols="100">' . $TextAnswer . '</textarea>';
			}
		} else {
			$qhtml .= '<textarea name="questionoptionst_' .  ($questionId) . '_' . ($masterQuestionId) . '[]" id="textarea"  class="form-control input-md clstextarea sectionTextarea_' . ($studentCoarcSectionId) . ' graduateTextarea_' . ($graduateCoarcSectionId) . ' personeelTextarea_' . ($personnelCoarcSectionId) . ' employerTextarea_' . ($employerCoarcSectionId) . '  " rows="4" cols="100"></textarea>';
		}
	} else {

		$objQuestionOption = new clsQuestionOption();
		// $getQuestionOptions = $objQuestionOption->GetPersonnelCoarcSchoolQuestionOptionsByQuestionId($questionId);
		// print_r($getQuestionOptions);exit;
		$totalCount = 0;
		if ($getQuestionOptions != '') {
			$totalCount = mysqli_num_rows($getQuestionOptions);
		}
		if ($totalCount > 0) {
			// echo $type;exit;
			$submittedChoiceArray = array();

			while ($row = mysqli_fetch_array($getQuestionOptions)) {

				$submittedChoiceArray = array();

				$summativeOptions = '';
				$QuestionOptions = $row["optionText"];
				$Questionvalues = $row["OptionValue"];
				$selectedOption = '';
				$explodeQuestionOption = explode("-", $QuestionOptions);
				$explodeQuestionOptionCount = count($explodeQuestionOption);

				if ($explodeQuestionOptionCount == 2)
					$summativeOptions = trim($explodeQuestionOption[0]);

				$strSeleted = '';
				if ($type == 10) {
					$qhtml .= '<input type="radio" id="radio"  checked="checked" name="questionoptions_' .  ($questionId) . '_' . $masterQuestionId . '[]" value="1" >Yes';
					$qhtml .= '<input type="radio" id="radio" name="questionoptions_' .  ($questionId) . '_' . $masterQuestionId . '[]" value="2"  >No';
				} else if ($type == 1 || $type == 2) {

					if ($isPositionFormative  == 1 || $isPositionCi  == 1 || $isPositionSite  == 1 || $isPositionSummative  == 1 || $isPositionMidterm  == 1)
						$isPositionClass = '';
					else
						$isPositionClass = 'some-class';

					$textColorClass = '';
					if ($QuestionOptions == '2' || $QuestionOptions == '1')
						$textColorClass = 'redColourToOptions';

					$strSeleted = "value='{$Questionvalues}'";

					if (count($submittedChoiceArray) && in_array($selectedOption, $submittedChoiceArray)) {
						//for edit
						$strSeleted = "value='{$selectedOption}' checked='checked'";
					} else if ($QuestionOptions == 'Yes' || $QuestionOptions == 'yes' || $summativeOptions == '3' || $summativeOptions == 'Progressing' || $QuestionOptions == '3' || $QuestionOptions == 'Sat' || $QuestionOptions == 'Minimally Okay' || $QuestionOptions == '3 = Average') {
						if (!count($submittedChoiceArray)) {
							$strSeleted = "value='{$Questionvalues}' checked='checked'";
						}
					}

					$qhtml .= "<div class='row {$isPositionClass}' style='margin:0px;'>
									<p style='padding-left:80px;' class ='{$textColorClass}'>
										<input type='radio' id='radio'  style='margin-left:-42px!important;' sectionId ='{$studentCoarcSectionId}' 
										graduatesectionId ='{$graduateCoarcSectionId}' personnelsectionId ='{$personnelCoarcSectionId}' 
										employersectionId ='{$employerCoarcSectionId}' 
										name='questionoptions_{$questionId}_{$masterQuestionId}[]' {$strSeleted} >&nbsp;&nbsp;{$QuestionOptions}
										<input type='hidden' name='hiddenRadioQuestion' value='{$questionId}' >
									</p>
								</div>";
				} else if ($type == 3) // Single CHeckbox
				{
					$checkboxClass = '';
					if ($QuestionOptions == 'Lab')
						$checkboxClass = 'isLab';
					else if ($QuestionOptions == 'Clinical')
						$checkboxClass = 'isClinical';

					$strSeleted = "value='{$OptionValue}'";
					if (count($submittedChoiceArray) && in_array($selectedOption, $submittedChoiceArray))
						$strSeleted = "value='{$selectedOption}' checked='checked'";

					$qhtml .= "<div class='row' style='margine:0px;'>
											<p style='padding-left:60px;'>
												<input type='checkbox' id='checkbox' style='margin-left:-42px!important;' class='{$checkboxClass}' name='chkquestionoptions[{$OptionValue}_{$questionId}]' {$strSeleted} >
													&nbsp;&nbsp;{$QuestionOptions}
												<input type='hidden' name='hiddenchkquestion' value='{$questionId}'>
											</p>
									</div>";
				} else if ($type == 4) // Single DropDown
				{
					$strSeleted = "value='{$Questionvalues}'";

					include_once('../class/clsschoolclinicalsiteunit.php');
					include_once('../class/clscheckoff.php');
					$checkoffId = 0;
					$GetClinician = '';
					$objClinician = new clsClinician();
					$objcheckoff = new clscheckoff();
					$Clinician = $objClinician->GetAllIntractionClinicians($schoolId);
					$checkoffId = $ID;
					$Selectedclinician = $objcheckoff->GetSelectedClinicianForAdvanceCheckoff($schoolId, $questionId, $checkoffId);
					$GetClinician = $Selectedclinician['clinicianId'];
					unset($objcheckoff);
					unset($objClinician);

					$qhtml .= "<select id='cboclinician' name='cboclinician_{$masterQuestionId}[]'>
									<option value='' selected>Select Preceptor</option>";
					if ($Clinician != '') {
						$totalCount = mysqli_num_rows($Clinician);
					}
					if ($totalCount > 0) {
						while ($row = mysqli_fetch_assoc($Clinician)) {
							$selclinicianId  = $row['clinicianId'];
							$firstName  = stripslashes($row['firstName']);
							$lastName  = stripslashes($row['lastName']);
							$name = $firstName . ' ' . $lastName;
							$selected = $GetClinician == $selclinicianId ? " selected='true'" : '';

							$qhtml .= "<option value='" . $row['clinicianId'] . "' " . $selected . ">" . $name . "</option>";
						}
					}
					$qhtml .= "</select>
								
						";
				} else if ($type == 6) //Single date
				{
					include_once('../class/clscheckoff.php');
					$objcheckoff = new clscheckoff();
					$SelectedDate = null;
					$GetDate = null;
					$checkoffId = 0;
					$checkoffId = $ID;

					$Selectedclinician = $objcheckoff->GetSelectedDateForAdvanceCheckoff($schoolId, $questionId, $checkoffId);
					$GetSingleDate = $Selectedclinician['SingleDate'];
					$Date = "value='{$GetSingleDate}'";
					unset($objcheckoff);

					$qhtml .= "<input type='date' id='date' name='datequestionoptions_{$questionId}[]' {$Date}>";
				} else if ($type == 7) // For bunch of Checkboxes,Date pickers and Dropdowns
				{
					$strSeleted = "value='{$Questionvalues}'";

					include_once('../class/clsschoolclinicalsiteunit.php');
					include_once('../class/clscheckoff.php');

					$getclinicianId = 0;
					$strSeleteddate = null;
					$objClinician = new clsClinician();
					$MyArray = array();
					$ClinicianList = $objClinician->GetAllCliniciansWithoutPreceptor($schoolId);
					while ($row = mysqli_fetch_array($ClinicianList)) {
						$clinicianId = $row['clinicianId'];
						$firstName = $row['firstName'];
						$lastName = $row['lastName'];
						$currentArray = array("clinicianId" => $clinicianId, "firstName" => $firstName, "lastName" => $lastName);
						array_push($MyArray, $currentArray);
					}

					$objcheckoff = new clscheckoff();
					$checkoffId = $ID;
					$SelectedPreceptor_array = array();
					$SelectedPreceptor = $objcheckoff->GetMultipleSelectedClinicianForAdvanceCheckoff($schoolId, $questionId, $checkoffId);
					$totalCheckOffDetail = 0;
					if ($SelectedPreceptor != '') {
						$totalCheckOffDetail = mysqli_num_rows($SelectedPreceptor);
						if ($totalCheckOffDetail > 0) {
							while ($row = mysqli_fetch_assoc($SelectedPreceptor)) {
								$SelectedPreceptor_array = $row;
							}

							if ((!empty($SelectedPreceptor_array['schoolOptionValue']) != '')) {
								//for edit
								$strSeleted = "value='{$selectedOption}' checked='checked'";
								$strSeleteddate = "value='{$SelectedPreceptor_array['completionDate']}'";
							}
						}
					}
					unset($objClinician);

					$qhtml .= "<div class='row' style='margine:0px;'>
										<p style='padding-left:60px;'>
												<input type='checkbox' id='checkbox' style='margin-left:-42px!important;' name='chkstage_{$questionId}' {$strSeleted} >
												&nbsp;&nbsp;{$QuestionOptions}
												
											<span style='padding-left:30px;'>{$QuestionOptions} Completion Date:&nbsp;&nbsp;
												<input type='date' id='date' style='margin-left:-0px!important;' name='datestage_{$questionId}' {$strSeleteddate} >
												&nbsp;&nbsp;
											</span>
											Clinical Instructor:&nbsp;&nbsp;
											<select id='cboclinician' name='cbostage_{$questionId}'>
											<option value='' selected>Select Clinical Instructor</option>";
					foreach ($MyArray as  $Clinician) {
						$selected = '';
						$strclinicianId = "value='{$Clinician['clinicianId']}'";
						if ($SelectedPreceptor != '') {
							$totalCheckOffDetail = mysqli_num_rows($SelectedPreceptor);
							if ($totalCheckOffDetail > 0) {
								if ($SelectedPreceptor_array['clinicianId'] == ($Clinician['clinicianId'])) {
									$selected = " selected='true'";
								}
							}
						} else {
							$selected = '';
						}
						$qhtml .= "<option value='{$Clinician['clinicianId']}' " . $selected . ">" . $Clinician['firstName'] . ' ' . $Clinician['lastName'] . "</option>";
					}
					$qhtml .= "</select>											
										</p>
								</div>";
				}
			}
		}
	}

	return $qhtml;
}

function GetEquipmentQuestionHtml($questionId, $type, $studentEquipmentMasterId, $schoolId)
{

	$objQuestionOption = new clsQuestionOption();
	$getQuestionOptions = $objQuestionOption->GetEquipmentQuestionOptions($questionId, $studentEquipmentMasterId);
	$getTextAns = $objQuestionOption->GetTextAnsOfEquipment($questionId, $studentEquipmentMasterId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}

function GetIncidentQuestionHtml($questionId, $type, $schoolIncidentQuestionId, $incidentId, $schoolId)
{
	$objQuestionOption = new clsQuestionOption();

	$getQuestionOptions = $objQuestionOption->GetAllIncidentQuestionMaster($questionId, $incidentId);
	$getTextAns = $objQuestionOption->GetTextAnsOfIncident($questionId, $incidentId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}
//for checkoff 3/9/2018
function GetCheckoffQuestionHtml($questionId, $type, $checkoffId, $schoolId, $isActiveCheckoff, $view, $isHLab = '', $schoolSectionId = 0)
{
	// echo '=>'.$schoolSectionId;
	$objQuestionOption = new clsQuestionOption();
	$getQuestionOptions = $objQuestionOption->GetAllQuestiondetail($questionId, $checkoffId, $schoolId, $isActiveCheckoff);
	$getTextAns = 'Checkoff';
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, $checkoffId, $schoolId, $view, $isHLab, $schoolSectionId);
}

//for checkoff 3/9/2018
function GetCheckoffQuestionHtml1($questionId, $type, $checkoffId, $schoolId, $isActiveCheckoff)
{
	$objQuestionOption = new clsQuestionOption();
	$getQuestionOptions = $objQuestionOption->GetAllQuestiondetailUSAF($questionId, $checkoffId, $schoolId, $isActiveCheckoff);
	$getTextAns = 'Checkoff';
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml1($getQuestionOptions, $type, $questionId, $getTextAns, $checkoffId, $schoolId);
}


function GetIrrQuestionHtml($questionId, $type, $irrMasterId, $schoolId, $LogedclinicianId, $irrDetailId)
{
	$objQuestionOption = new clsQuestionOption();
	$getTextAns = 0;
	$getQuestionOptions = $objQuestionOption->GetIrrQuestiondetail($questionId, $irrMasterId, $schoolId, $irrDetailId);
	$getTextAns = $objQuestionOption->GetTextAnsOfIrr($questionId, $irrMasterId, $schoolId, $irrDetailId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}

function GetCheckoffSingleQuestionHtml($questionId, $type, $schoolId)
{
	$objQuestionOption = new clsQuestionOption();
	$getTextAns = 0;
	$getQuestionOptions = $objQuestionOption->GetsingleQuestiondetail($questionId);
	$getTextAns = $objQuestionOption->GetTextAnsOfsingleQuestion($questionId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}


function GetMasterCheckoffSingleQuestionHtml($questionId, $type, $schoolId)
{
	$objQuestionOption = new clsQuestionOption();
	$getTextAns = 0;
	$getQuestionOptions = $objQuestionOption->GetMastersingleQuestiondetail($questionId);
	$getTextAns = $objQuestionOption->GetTextAnsOfMastersingleQuestiondetail($questionId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}

function GetUsafMasterCheckoffSingleQuestionHtml($questionId, $type, $schoolId)
{
	$objQuestionOption = new clsQuestionOption();
	$getTextAns = 0;
	$getQuestionOptions = $objQuestionOption->GetUsafMastersingleQuestiondetail($questionId);
	$getTextAns = $objQuestionOption->GetTextAnsOfUsafMastersingleQuestiondetail($questionId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}

function RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, $ID, $schoolId, $view = '', $isHLab = '', $schoolSectionId = 0)
{

	//    echo '------type'.$type;
	//    echo '------schoolSectionId'.$schoolSectionId;
	$qhtml = '';
	$disabled = '';
	//Get Coarc Section Ids
	include_once('../class/clsStudentCoarcMaster.php');
	include_once('../class/clsschoolclinicalsiteunit.php');
	include_once('../class/clscheckoff.php');
	include_once('../class/clsExternalPreceptors.php');
	$StudentCoarcMaster = new clsStudentCoarcMaster();
	$sectionIds = $StudentCoarcMaster->GetCoarcSectionIdId($questionId);
	unset($StudentCoarcMaster);

	$studentCoarcSectionId = $sectionIds['studentCoarcSectionId'] ? $sectionIds['studentCoarcSectionId'] : 0;
	$employerCoarcSectionId = $sectionIds['employerCoarcSectionId'] ? $sectionIds['employerCoarcSectionId'] : 0;
	$graduateCoarcSectionId = $sectionIds['graduateCoarcSectionId'] ? $sectionIds['graduateCoarcSectionId'] : 0;
	$personnelCoarcSectionId = $sectionIds['personnelCoarcSectionId'] ? $sectionIds['personnelCoarcSectionId'] : 0;

	$isPositionFormative = '';
	$isPositionCi = '';
	$isPositionSummative = '';
	$isPositionSite = '';
	$isPositionMidterm = '';
	$isPosition = '';
	// OPS Added Condition || $view !='V' Date:-8 Oct 2021
	if ($view == 'V') {
		$disabled = 'disabled';
	} else {
		$disabled = '';
	}
	//Display TextArea 
	if ($type == 5) {
		if ($getTextAns == 'Checkoff') {
			$objQuestionOption = new clsQuestionOption();
			$getTextAns = $objQuestionOption->GetTextAnsOfCheckoff($questionId, $ID, $schoolId);
			unset($objQuestionOption);
		}

		$totalTextCount = mysqli_num_rows($getTextAns);
		if ($totalTextCount > 0) {
			while ($rows = mysqli_fetch_array($getTextAns)) {
				$TextAnswer = '';
				$TextAnswer = $rows["TextAnswer"];
				$qhtml .= '<textarea name="questionoptionst_' .  ($questionId) . '[]" id="textarea" class="form-control input-md clstextarea" ' . $disabled . '  rows="4" cols="100">' . $TextAnswer . '</textarea>';
			}
		} else {
			$qhtml .= '<textarea name="questionoptionst_' .  ($questionId) . '[]" id="textarea" ' . $disabled . '  class="form-control input-md clstextarea sectionTextarea_' . ($studentCoarcSectionId) . ' graduateTextarea_' . ($graduateCoarcSectionId) . ' personeelTextarea_' . ($personnelCoarcSectionId) . ' employerTextarea_' . ($employerCoarcSectionId) . '  " rows="4" cols="100"></textarea>';
		}
	} else {
		$totalCount = 0;
		if ($getQuestionOptions != '') {
			$totalCount = mysqli_num_rows($getQuestionOptions);
		}

		if ($totalCount > 0) {


			$submittedChoiceArray = array();

			while ($row = mysqli_fetch_array($getQuestionOptions)) {
				if ($row["SelectedOptionValue"])
					$submittedChoiceArray[] = $row["SelectedOptionValue"];

				$summativeOptions = '';
				$QuestionOptions = $row["optionText"];
				$OptionValue = $row["OptionValue"];
				$questionComment = isset($row["questionComment"]) ? $row["questionComment"] : '';
				$Questionvalues = isset($row["schoolOptionValue"]) ? $row["schoolOptionValue"] : '';
				$selectedOption = $row['SelectedOptionValue'];
				$isPosition = isset($row["isPosition"]) ? $row["isPosition"] : 0;
				$explodeQuestionOption = explode("-", $QuestionOptions);
				$explodeQuestionOptionCount = count($explodeQuestionOption);
				if ($explodeQuestionOptionCount >= 2)
					$summativeOptions = trim($explodeQuestionOption[0]);

				$strSeleted = '';
				// print_r($submittedChoiceArray);
				if ($type == 10) {
					$qhtml .= '<input type="radio" id="radio"  checked="checked" name="questionoptions_' .  ($questionId) . '[]" ' . $isHLab . ' value="1" >Yes';
					$qhtml .= '<input type="radio" id="radio" name="questionoptions_' .  ($questionId) . '[]" value="2" ' . $isHLab . '  >No';
				} else if ($type == 1 || $type == 2) {

					if ($isPositionFormative  == 1 || $isPositionCi  == 1 || $isPositionSite  == 1 || $isPositionSummative  == 1 || $isPositionMidterm  == 1 || $isPosition == 1)
						$isPositionClass = '';
					else
						$isPositionClass = 'some-class';

					$textColorClass = '';
					if ($QuestionOptions == '2' || $QuestionOptions == '1')
						$textColorClass = 'redColourToOptions';

					$strSeleted = "value='{$OptionValue}'";

					if (count($submittedChoiceArray) && in_array($selectedOption, $submittedChoiceArray)) {

						//for edit
						$strSeleted = "value='{$selectedOption}' checked='checked'";
					} else if ($QuestionOptions == 'Yes' || $QuestionOptions == '1' || $QuestionOptions == 'yes' || $summativeOptions == '3' || $summativeOptions == 'Progressing' || $QuestionOptions == '3' || $QuestionOptions == 'Sat' || $QuestionOptions == 'Minimally Okay' || $QuestionOptions == '3 = Average') {
						// echo '</br>QuestionOptions '. $QuestionOptions;
						// echo '</br>count '.count($submittedChoiceArray);
						// echo '</br>textColorClass '.($textColorClass);
						if (!count($submittedChoiceArray) && $schoolId != 125) {
							if ($textColorClass == 'redColourToOptions' && $QuestionOptions == '1') {
							} else {
								$strSeleted = "value='{$OptionValue}' checked='checked'";
							}
						}
					}
					// echo '</br>strSeleted '. $strSeleted;

					$qhtml .= "<div class='row {$isPositionClass}' style='margine:0px;'><p style='padding-left:80px;' class ='{$textColorClass}'><input type='radio' id='radio' {$disabled} {$isHLab} style='margin-left:-42px!important;'  data-parsley-errors-container='#error-{$questionId}'  sectionId ='{$studentCoarcSectionId}' graduatesectionId ='{$graduateCoarcSectionId}' personnelsectionId ='{$personnelCoarcSectionId}' employersectionId ='{$employerCoarcSectionId}' name='questionoptions_{$questionId}[]' {$strSeleted} >&nbsp;&nbsp;{$QuestionOptions}
									<input type='hidden' name='hiddenRadioQuestion' value='{$questionId}' ></p>
									</div>";
				} else if ($type == 3) // Single CHeckbox
				{

					$checkboxClass = '';
					if ($QuestionOptions == 'Lab')
						$checkboxClass = 'isLab';
					else if ($QuestionOptions == 'Clinical')
						$checkboxClass = 'isClinical';

					$isPositionClass = ($isPosition == 1) ? '' : 'some-class';

					$strSeleted = "value='{$OptionValue}'";
					if (count($submittedChoiceArray) && in_array($selectedOption, $submittedChoiceArray))
						$strSeleted = "value='{$selectedOption}' checked='checked'";

					$qhtml .= "<div class='row {$isPositionClass} mb-2' style='margin:0;'>
                    <div class='col-12'>
                        <div class='form-check' 
                             style='padding-left: 10px; padding-top: 2px; " . 
                             ($isPositionClass == 'vertical' 
                                ? "display: block;" 
                                : "display: flex; align-items: flex-start; flex-wrap: wrap; gap: 12px;") . "'>
             
                            <input type='checkbox' class='form-check-input {$checkboxClass}'
                                id='checkbox_{$OptionValue}_{$questionId}'
                                isSectionId='{$schoolSectionId}'
                                name='chkquestionoptions[{$OptionValue}_{$questionId}]'
                                {$disabled} {$strSeleted}
                                style='margin-top: 3px;'>

                            <label class='form-check-label' 
                                for='checkbox_{$OptionValue}_{$questionId}' 
                                style='margin-left: 5px; margin-bottom: 0; word-break: break-word;'>
                                {$QuestionOptions}
                            </label>

                            <input type='hidden' name='hiddenchkquestion' value='{$questionId}'>
                        </div>
                    </div>
                </div>";

				} else if ($type == 4) // Single DropDown
				{
					$strSeleted = "value='{$OptionValue}'";


					$checkoffId = 0;
					$GetClinician = '';

					$objClinician = new clsClinician();
					$objcheckoff = new clscheckoff();
					$Clinician = $objClinician->GetAllIntractionClinicians($schoolId);
					$checkoffId = $ID;
					$Selectedclinician = $objcheckoff->GetSelectedClinicianForAdvanceCheckoff($schoolId, $questionId, $checkoffId);
					$GetClinician = $Selectedclinician['clinicianId'];



					$qhtml .= "<select class='form-control select2_single' id='cboclinician' name='cboclinician_{$questionId}[]'>
									<option value='' selected>Select Instructor</option>";
					if ($Clinician != '') {
						$totalCount = mysqli_num_rows($Clinician);
					}
					if ($totalCount > 0) {
						while ($row = mysqli_fetch_assoc($Clinician)) {
							$selclinicianId  = $row['clinicianId'];
							$firstName  = stripslashes($row['firstName']);
							$lastName  = stripslashes($row['lastName']);
							$name = $firstName . ' ' . $lastName;
							$selected = $GetClinician == $selclinicianId ? " selected='true'" : '';

							$qhtml .= "<option value='" . $row['clinicianId'] . "' " . $selected . " >" . $name . "</option>";
						}
					}
					$qhtml .= "</select>
								
						";
				} else if ($type == 6) //Single date
				{


					$SelectedDate = null;
					$GetDate = null;
					$checkoffId = 0;
					$checkoffId = $ID;
					$Date = "value=''";
					$objcheckoff = new clscheckoff();
					$Selectedclinician = $objcheckoff->GetSelectedDateForAdvanceCheckoff($schoolId, $questionId, $checkoffId);
					if ($Selectedclinician) {

						$GetSingleDate =  ($Selectedclinician['SingleDate'] != '0000-00-00' && $Selectedclinician['SingleDate'] != '') ? date('m/d/Y', strtotime($Selectedclinician['SingleDate'])) : '';
						$Date = "value='{$GetSingleDate}'";
					}

					unset($objcheckoff);

					$qhtml .= "<!-- <input class='form-control' type='date' id='date1' {$disabled} {$schoolId} {$questionId} {$checkoffId} name='datequestionoptions_{$questionId}[]' {$Date}> -->
					<div class='input-group date w-full' id='completionDate' style='position: relative;'>

								<input type='text' name='datequestionoptions_{$questionId}[]' {$disabled} {$schoolId} {$questionId} {$checkoffId} id='date1' class='form-control input-md rotation_date dateInputFormat completionDate' {$Date}  data-parsley-errors-container='#error-txtDate' placeholder='MM-DD-YYYY' />
								<span class='input-group-addon calender-icon'>
									<span class='glyphicon glyphicon-calendar'></span>
								</span>
							</div>
					
					";
				} else if ($type == 7) // For bunch of Checkboxes,Date pickers and Dropdowns
				{
					$strSeleted = "value='{$OptionValue}'";

					$checkoffId = $ID;
					$objcheckoff = new clscheckoff();
					$checkoffDetail = $objcheckoff->GetCHeckoffDetailByCheckoffID($checkoffId);
					$isExternalPreceptorcheckoff = isset($checkoffDetail['isExternalPreceptorcheckoff']) ? $checkoffDetail['isExternalPreceptorcheckoff'] : 0;

					$preceptorIds = array(
						$checkoffDetail['completion1stPreceptorId'],
						$checkoffDetail['completion2ndPreceptorId'],
						$checkoffDetail['completion3rdPreceptorId'],
						$checkoffDetail['completion4thPreceptorId'],
						$checkoffDetail['completion5thPreceptorId']
					);

					// Filter out values greater than 0
					$filteredPreceptorIds = array_filter($preceptorIds, function ($value) {
						return $value > 0;
					});

					$preceptorIdsCount = count($filteredPreceptorIds);
					$preceptorIdsCount = ($preceptorIdsCount) ? $preceptorIdsCount : 0;
					// print_r($filteredPreceptorIds);
					// Create a comma-separated string of filtered values
					$preceptorIds = implode(',', $filteredPreceptorIds);


					$getclinicianId = 0;
					$strSeleteddate = null;
					$objClinician = new clsClinician();
					$objExternalPreceptor = new clsExternalPreceptors();
					$MyArray = array();

					//Check this Checkoff is External Preceptor Checkoff or Not.
					if ($isExternalPreceptorcheckoff && $preceptorIdsCount)
						$ClinicianList = $objExternalPreceptor->GetAllExternalPrecptorsBySchool($schoolId, $checkoffId, $preceptorIds);
					else
						$ClinicianList = $objClinician->GetAllCliniciansWithoutPreceptor($schoolId);


					while ($row = mysqli_fetch_array($ClinicianList)) {
						$clinicianId = $row['clinicianId'];
						$firstName = $row['firstName'];
						$lastName = $row['lastName'];
						$currentArray = array("clinicianId" => $clinicianId, "firstName" => $firstName, "lastName" => $lastName);
						array_push($MyArray, $currentArray);
					}


					$SelectedPreceptor_array = array();
					// if($isExternalPreceptorcheckoff)
					// 	$SelectedPreceptor = $objExternalPreceptor->GetAllExternalPrecptorsBySchool($schoolId);
					// else
					$SelectedPreceptor = $objcheckoff->GetMultipleSelectedClinicianForAdvanceCheckoff($schoolId, $questionId, $checkoffId);

					$totalCheckOffDetail = 0;
					if ($SelectedPreceptor != '') {
						$totalCheckOffDetail = mysqli_num_rows($SelectedPreceptor);
						if ($totalCheckOffDetail > 0) {
							while ($row = mysqli_fetch_assoc($SelectedPreceptor)) {
								$SelectedPreceptor_array = $row;
							}

							if ((!empty($SelectedPreceptor_array['schoolOptionValue']) != '')) {
								//for edit
								$strSeleted = "value='{$selectedOption}' checked='checked'";
								$completionDate = date('m/d/Y', strtotime($SelectedPreceptor_array['completionDate']));
								$strSeleteddate = "value='{$completionDate}'";
							}
						}
					}
					$selectLabel = 'Preceptor/Clinical Instructor';
					// $selectLabel = ($isExternalPreceptorcheckoff) ? 'Preceptor/Clinical Instructor' : 'Clinical Instructor';
					$qhtml .= "<div class='row' style='margin:0px;'>
										<p class='completion-section'>
												<input type='checkbox' id='checkbox' name='chkstage_{$questionId}' {$strSeleted} >
												&nbsp;&nbsp;{$QuestionOptions}
												<div class='mobile-block'>
													<div class='completion-date-section'>
											<span>{$QuestionOptions} Completion Date:&nbsp;&nbsp;
												<!-- <input class='form-control completionDate' type='text' id='date' style='margin-left:-0px!important;' name='datestage_{$questionId}' {$strSeleteddate} > -->
												&nbsp;&nbsp;
												<div class='input-group date w-full' id='completionDate' style='position: relative;'>

								<input type='text' name='datestage_{$questionId}' id='date' class='form-control input-md rotation_date dateInputFormat completionDate' {$strSeleteddate}  data-parsley-errors-container='#error-txtDate' placeholder='MM-DD-YYYY' />
								<span class='input-group-addon calender-icon'>
									<span class='glyphicon glyphicon-calendar'></span>
								</span>
							</div>
											</span>
										</div>
										<div class='completion-date-section'>
											
											<span>
											{$selectLabel}:&nbsp;&nbsp;
										  <div class='  w-full' style='width:100% !important'>
											<select class='form-control cboclinician select2_single w-full' id='cboclinician' name='cbostage_{$questionId}' >
											<option value='' selected>Select</option>";
					foreach ($MyArray as  $Clinician) {
						$selected = '';
						$strclinicianId = "value='{$Clinician['clinicianId']}'";
						if ($SelectedPreceptor != '') {
							$totalCheckOffDetail = mysqli_num_rows($SelectedPreceptor);
							if ($totalCheckOffDetail > 0) {
								if ($isExternalPreceptorcheckoff && $preceptorIdsCount && ($SelectedPreceptor_array['externalPreceptorId'] == ($Clinician['clinicianId'])))
									$selected = " selected='true'";
								else if (($SelectedPreceptor_array['clinicianId'] == ($Clinician['clinicianId'])))
									$selected = " selected='true'";
							}
						} else {
							$selected = '';
						}
						$qhtml .= "<option value='{$Clinician['clinicianId']}' " . $selected . ">" . $Clinician['firstName'] . ' ' . $Clinician['lastName'] . "</option>";
					}
					$qhtml .= "</select>
					</div>	
				</span>		
			</div>				
			</div>				
										</p>
								</div>";
				} else if ($type == 9) {
					if ($isPositionFormative  == 1 || $isPositionCi  == 1 || $isPositionSite  == 1 || $isPositionSummative  == 1 || $isPositionMidterm  == 1)
						$isPositionClass = '';
					else
						$isPositionClass = 'some-class';

					$textColorClass = '';
					if ($QuestionOptions == '2' || $QuestionOptions == '1')
						$textColorClass = 'redColourToOptions';

					$strSeleted = "value='{$OptionValue}'";

					if (count($submittedChoiceArray) && in_array($selectedOption, $submittedChoiceArray)) {
						//for edit
						$strSeleted = "value='{$selectedOption}' checked='checked'";
					} else if ($QuestionOptions == 'Yes' || $QuestionOptions == '1' || $QuestionOptions == 'yes' || $summativeOptions == '3' || $summativeOptions == 'Progressing' || $QuestionOptions == '3' || $QuestionOptions == 'Sat' || $QuestionOptions == 'Minimally Okay' || $QuestionOptions == '3 = Average') {
						if (!count($submittedChoiceArray) && $schoolId != 125) {
							$strSeleted = "value='{$OptionValue}' checked='checked'";
						}
					}

					$qhtml .= "<div class='row {$isPositionClass}' style='margin:0px;'><p style='padding-left:50px;' class ='{$textColorClass}'><input type='radio' id='radio' {$disabled} {$isHLab} style='margin-left:-42px!important;'  data-parsley-errors-container='#error-{$questionId}'  sectionId ='{$studentCoarcSectionId}' graduatesectionId ='{$graduateCoarcSectionId}' personnelsectionId ='{$personnelCoarcSectionId}' employersectionId ='{$employerCoarcSectionId}' name='mansfieldquestionoptions_{$questionId}[]' {$strSeleted} >&nbsp;&nbsp;{$QuestionOptions}
							<input type='hidden' name='hiddenRadioQuestion' value='{$questionId}' ></p>
						</div>";
				}
			}

			unset($objClinician);
			unset($objcheckoff);
		}
	}

	return $qhtml;
}

function RenderQuestionChoicesHtml1($getQuestionOptions, $type, $questionId, $getTextAns, $ID, $schoolId)
{

	$qhtml = '';
	//Get Coarc Section Ids
	include_once('../class/clsStudentCoarcMaster.php');
	$StudentCoarcMaster = new clsStudentCoarcMaster();
	$sectionIds = $StudentCoarcMaster->GetCoarcSectionIdId($questionId);
	$studentCoarcSectionId = $sectionIds['studentCoarcSectionId'] ? $sectionIds['studentCoarcSectionId'] : 0;
	$employerCoarcSectionId = $sectionIds['employerCoarcSectionId'] ? $sectionIds['employerCoarcSectionId'] : 0;
	$graduateCoarcSectionId = $sectionIds['graduateCoarcSectionId'] ? $sectionIds['graduateCoarcSectionId'] : 0;
	$personnelCoarcSectionId = $sectionIds['personnelCoarcSectionId'] ? $sectionIds['personnelCoarcSectionId'] : 0;

	$isPositionFormative = '';
	$isPositionCi = '';
	$isPositionSummative = '';
	$isPositionSite = '';
	$isPositionMidterm = '';

	//Display TextArea 
	if ($type == 5) {

		if ($getTextAns == 'Checkoff') {
			$objQuestionOption = new clsQuestionOption();
			$getTextAns = $objQuestionOption->GetTextAnsOfCheckoff($questionId, $ID, $schoolId);
			unset($objQuestionOption);
		}

		$totalTextCount = mysqli_num_rows($getTextAns);
		if ($totalTextCount > 0) {
			while ($rows = mysqli_fetch_array($getTextAns)) {
				$TextAnswer = '';
				$TextAnswer = $rows["TextAnswer"];
				$qhtml .= '<textarea name="questionoptionst_' .  ($questionId) . '[]" id="textarea" class="form-control input-md clstextarea" rows="4" cols="100">' . $TextAnswer . '</textarea>';
			}
		} else {
			$qhtml .= '<textarea name="questionoptionst_' .  ($questionId) . '[]" id="textarea"  class="form-control input-md clstextarea sectionTextarea_' . ($studentCoarcSectionId) . ' graduateTextarea_' . ($graduateCoarcSectionId) . ' personeelTextarea_' . ($personnelCoarcSectionId) . ' employerTextarea_' . ($employerCoarcSectionId) . '  " rows="4" cols="100"></textarea>';
		}
	} else {

		$totalCount = 0;
		if ($getQuestionOptions != '') {
			//   echo "hii";
			$totalCount = mysqli_num_rows($getQuestionOptions);
			// echo $totalCount;
		}

		if ($totalCount > 0) {

			$submittedChoiceArray = array();

			while ($row = mysqli_fetch_array($getQuestionOptions)) {

				if ($row["SelectedOptionValue"])
					$submittedChoiceArray[] = $row["SelectedOptionValue"];

				$summativeOptions = '';
				$QuestionOptions = $row["optionText"];
				$OptionValue = $row["OptionValue"];
				$Questionvalues = $row["schoolOptionValue"];
				$selectedOption = $row['SelectedOptionValue'];
				$explodeQuestionOption = explode("-", $QuestionOptions);
				$explodeQuestionOptionCount = count($explodeQuestionOption);

				if ($explodeQuestionOptionCount == 2)
					$summativeOptions = trim($explodeQuestionOption[0]);

				$strSeleted = '';

				if ($type == 10) {
					$qhtml .= '<input type="radio" id="radio"  checked="checked" name="questionoptions_' .  ($questionId) . '[]" value="1" >Yes';
					$qhtml .= '<input type="radio" id="radio" name="questionoptions_' .  ($questionId) . '[]" value="2"  >No';
				} else if ($type == 1 || $type == 2) {
					if ($isPositionFormative  == 1 || $isPositionCi  == 1 || $isPositionSite  == 1 || $isPositionSummative  == 1 || $isPositionMidterm  == 1)
						$isPositionClass = '';
					else
						$isPositionClass = 'some-class';

					$textColorClass = '';
					if ($QuestionOptions == '2' || $QuestionOptions == '1')
						$textColorClass = 'redColourToOptions';

					$strSeleted = "value='{$OptionValue}'";

					if (count($submittedChoiceArray) && in_array($selectedOption, $submittedChoiceArray)) {

						//for edit
						$strSeleted = "value='{$selectedOption}' checked='checked'";
					} else if ($QuestionOptions == 'Yes' || $QuestionOptions == 'yes' || $summativeOptions == '3' || $summativeOptions == 'Progressing' || $QuestionOptions == '3' || $QuestionOptions == 'Sat' || $QuestionOptions == 'Minimally Okay' || $QuestionOptions == '3 = Average') {
						if (!count($submittedChoiceArray)) {
							$strSeleted = "value='{$OptionValue}' checked='checked'";
						}
					}
					$dataMarks = 'data-marks=0';

					if ($schoolId == 122) {
						if ($QuestionOptions == 'Yes') {
							$objDB = new clsDB();
							$marks = $objDB->GetSingleColumnValueFromTable('schooldefaultquestionmaster', 'marks', 'schoolQuestionId', $questionId);
							unset($objDB);
							$dataMarks = 'data-marks=' . $marks;
							// if($marks)
						}
					}
					$qhtml .= "<div class='row {$isPositionClass}' style='margine:0px;'><p style='padding-left:80px;' class ='{$textColorClass}'><input type='radio' id='radio'  style='margin-left:-42px!important;' sectionId ='{$studentCoarcSectionId}' graduatesectionId ='{$graduateCoarcSectionId}' personnelsectionId ='{$personnelCoarcSectionId}' employersectionId ='{$employerCoarcSectionId}' $dataMarks name='questionoptions_{$questionId}[]' {$strSeleted} >&nbsp;&nbsp;{$QuestionOptions}
									<input type='hidden' name='hiddenRadioQuestion' value='{$questionId}' ></p></div>";
				} else if ($type == 3) // Single CHeckbox
				{
					$checkboxClass = '';
					if ($QuestionOptions == 'Lab')
						$checkboxClass = 'isLab';
					else if ($QuestionOptions == 'Clinical')
						$checkboxClass = 'isClinical';

					$strSeleted = "value='{$OptionValue}'";
					if (count($submittedChoiceArray) && in_array($selectedOption, $submittedChoiceArray))
						$strSeleted = "value='{$selectedOption}' checked='checked'";

					$qhtml .= "<div class='row' style='margine:0px;'>
											<p style='padding-left:60px;'>
												<input type='checkbox' id='checkbox' style='margin-left:-42px!important;' class='{$checkboxClass}' name='chkquestionoptions[{$OptionValue}_{$questionId}]' {$strSeleted} >
													&nbsp;&nbsp;{$QuestionOptions}
												<input type='hidden' name='hiddenchkquestion' value='{$questionId}'>
											</p>
									</div>";
				} else if ($type == 4) // Single DropDown
				{
					$strSeleted = "value='{$OptionValue}'";

					include_once('../class/clsschoolclinicalsiteunit.php');
					include_once('../class/clscheckoff.php');
					$checkoffId = 0;
					$GetClinician = '';
					$objClinician = new clsClinician();
					$objcheckoff = new clscheckoff();
					$Clinician = $objClinician->GetAllIntractionClinicians($schoolId);
					$checkoffId = $ID;
					$Selectedclinician = $objcheckoff->GetSelectedClinicianForAdvanceCheckoff($schoolId, $questionId, $checkoffId);
					$GetClinician = $Selectedclinician['clinicianId'];
					unset($objcheckoff);
					unset($objClinician);

					$qhtml .= "<select id='cboclinician' name='cboclinician_{$questionId}[]'>
									<option value='' selected>Select Preceptor</option>";
					if ($Clinician != '') {
						$totalCount = mysqli_num_rows($Clinician);
					}
					if ($totalCount > 0) {
						while ($row = mysqli_fetch_assoc($Clinician)) {
							$selclinicianId  = $row['clinicianId'];
							$firstName  = stripslashes($row['firstName']);
							$lastName  = stripslashes($row['lastName']);
							$name = $firstName . ' ' . $lastName;
							$selected = $GetClinician == $selclinicianId ? " selected='true'" : '';

							$qhtml .= "<option value='" . $row['clinicianId'] . "' " . $selected . ">" . $name . "</option>";
						}
					}
					$qhtml .= "</select>
								
						";
				} else if ($type == 6) //Single date
				{
					include_once('../class/clscheckoff.php');
					$objcheckoff = new clscheckoff();
					$SelectedDate = null;
					$GetDate = null;
					$checkoffId = 0;
					$checkoffId = $ID;

					$Selectedclinician = $objcheckoff->GetSelectedDateForAdvanceCheckoff($schoolId, $questionId, $checkoffId);
					$GetSingleDate = $Selectedclinician['SingleDate'];
					$Date = "value='{$GetSingleDate}'";
					unset($objcheckoff);

					$qhtml .= "<input type='date' id='date' name='datequestionoptions_{$questionId}[]' {$Date}>";
				} else if ($type == 7) // For bunch of Checkboxes,Date pickers and Dropdowns
				{
					$strSeleted = "value='{$OptionValue}'";

					include_once('../class/clsschoolclinicalsiteunit.php');
					include_once('../class/clscheckoff.php');

					$getclinicianId = 0;
					$strSeleteddate = null;
					$objClinician = new clsClinician();
					$MyArray = array();
					$ClinicianList = $objClinician->GetAllCliniciansWithoutPreceptor($schoolId);
					while ($row = mysqli_fetch_array($ClinicianList)) {
						$clinicianId = $row['clinicianId'];
						$firstName = $row['firstName'];
						$lastName = $row['lastName'];
						$currentArray = array("clinicianId" => $clinicianId, "firstName" => $firstName, "lastName" => $lastName);
						array_push($MyArray, $currentArray);
					}

					$objcheckoff = new clscheckoff();
					$checkoffId = $ID;
					$SelectedPreceptor_array = array();
					$SelectedPreceptor = $objcheckoff->GetMultipleSelectedClinicianForAdvanceCheckoff($schoolId, $questionId, $checkoffId);
					$totalCheckOffDetail = 0;
					if ($SelectedPreceptor != '') {
						$totalCheckOffDetail = mysqli_num_rows($SelectedPreceptor);
						if ($totalCheckOffDetail > 0) {
							while ($row = mysqli_fetch_assoc($SelectedPreceptor)) {
								$SelectedPreceptor_array = $row;
							}

							if ((!empty($SelectedPreceptor_array['schoolOptionValue']) != '')) {
								//for edit
								$strSeleted = "value='{$selectedOption}' checked='checked'";
								$strSeleteddate = "value='{$SelectedPreceptor_array['completionDate']}'";
							}
						}
					}
					unset($objClinician);

					$qhtml .= "<div class='row' style='margine:0px;'>
										<p style='padding-left:60px;'>
												<input type='checkbox' id='checkbox' style='margin-left:-42px!important;' name='chkstage_{$questionId}' {$strSeleted} >
												&nbsp;&nbsp;{$QuestionOptions}
												
											<span style='padding-left:30px;'>{$QuestionOptions} Completion Date:&nbsp;&nbsp;
												<input type='date' id='date' style='margin-left:-0px!important;' name='datestage_{$questionId}' {$strSeleteddate} >
												&nbsp;&nbsp;
											</span>
											Clinical Instructor:&nbsp;&nbsp;
											<select id='cboclinician' name='cbostage_{$questionId}'>
											<option value='' selected>Select Clinical Instructor</option>";
					foreach ($MyArray as  $Clinician) {
						$selected = '';
						$strclinicianId = "value='{$Clinician['clinicianId']}'";
						if ($SelectedPreceptor != '') {
							$totalCheckOffDetail = mysqli_num_rows($SelectedPreceptor);
							if ($totalCheckOffDetail > 0) {
								if ($SelectedPreceptor_array['clinicianId'] == ($Clinician['clinicianId'])) {
									$selected = " selected='true'";
								}
							}
						} else {
							$selected = '';
						}
						$qhtml .= "<option value='{$Clinician['clinicianId']}' " . $selected . ">" . $Clinician['firstName'] . ' ' . $Clinician['lastName'] . "</option>";
					}
					$qhtml .= "</select>											
										</p>
								</div>";
				}
			}
		}
	}

	return $qhtml;
}

function ReturnResults($resultArray)
{
	header('Content-type: application/json');
	echo json_encode($resultArray);
	exit();
}
function ValidateApiToken()
{
	$resultsArray = array();
	$resultsArray['status'] = '0';
	$resultsArray['errorCode'] = '001';

	if (isset($_POST['APITOKEN']) && isset($_POST['userId'])) {
		$userId = DecodeQueryData($_POST['userId']);
		//Validate request
		if ($_POST['APITOKEN'] != EncodeQueryData($userId . API_TOKEN)) {
			$resultsArray['message'] = 'Un-Authorized access.';
			ReturnResults($resultsArray);
		}
	} else {
		$resultsArray['message'] = 'API TOKEN  missing.';
		ReturnResults($resultsArray);
	}
}
function ValidateApiAccessKey($ApiAccessKey)
{
	$ApiAccessKeye = API_ACCESS_KEY;
	$ApiAccessKey = ($ApiAccessKey);

	if ($ApiAccessKeye == $ApiAccessKey) {
		return true;
	} else {
		return false;
	}
}

function ShowError($errorCode, $errorMessage, $data = array())
{
	header('Content-type: application/json');
	$Error = array(
		"ErrorCode" => $errorCode,
		"ErrorMessage" => $errorMessage
	);

	if (count($data)) {
		foreach ($data as $key => $value) {
			$Error[$key] = $value;
		}
	}
	echo json_encode(array('Error' => $Error));
	exit();
}

function GenerateUserAccessToken($UniqueId)
{
	$UniqueId = trim($UniqueId);
	$ENCRYPTION_KEY = '252-85DA2S3-ADSS5D-EI5B4A221';
	$secretKey = $ENCRYPTION_KEY . '' . $UniqueId;
	return base64_encode($secretKey);
}

// Generate Access token for 24 hrs
// function GenerateUserAccessToken($UniqueId)
// {
// 	$UniqueId = trim($UniqueId);
// 	$ENCRYPTION_KEY = '252-85DA2S3-ADSS5D-EI5B4A221';
// 	$expirationTimestamp = time() + (24 * 60 * 60); // Current timestamp + 24 hours

// 	// Append the expiration timestamp to the secret key
// 	$secretKey = $ENCRYPTION_KEY . '' . $UniqueId . $expirationTimestamp;

// 	return base64_encode($secretKey);
// }

function ValidateUserAccessToken($UniqueId, $AccessToken)
{
	$UniqueId = trim($UniqueId);
	$ENCRYPTION_KEY = '252-85DA2S3-ADSS5D-EI5B4A221';
	$secretKey = $ENCRYPTION_KEY . '' . $UniqueId;
	$secretKey = base64_encode($secretKey);
	$AccessToken = trim($AccessToken);

	if ($secretKey == $AccessToken) {
		return true;
	} else {
		return false;
	}
}
//Validate access token for 24hrs
// function ValidateUserAccessToken($UniqueId, $AccessToken)
// {
// 	$UniqueId = trim($UniqueId);
// 	$ENCRYPTION_KEY = '252-85DA2S3-ADSS5D-EI5B4A221';
// 	$secretKey = $ENCRYPTION_KEY . '' . $UniqueId;
// 	$secretKey = base64_encode($secretKey);
// 	$AccessToken = trim($AccessToken);

// 	// Decode the secret key to retrieve the original value
// 	$decodedSecretKey = base64_decode($AccessToken);

// 	// Extract the expiration timestamp from the secret key
// 	$expirationTimestamp = substr($decodedSecretKey, -10);

// 	if ($secretKey == $AccessToken && time() <= $expirationTimestamp) {
// 		return true;
// 	} else {
// 		return false;
// 	}
// }


function ValidateAccessSPassword($ApiAccessSPassword)
{
	$ApiAccessSPasswords = API_ACCESS_PASSWORD;
	$ApiAccessSPassword = ($ApiAccessSPassword);
	if ($ApiAccessSPasswords == $ApiAccessSPassword) {
		return true;
	} else {
		return false;
	}
}

function converToServerTimeZone($time = "", $timezone, $isApp = 0)
{

	if ($isApp) {
		$datetime = new DateTime($time);
		$la_time = new DateTimeZone($timezone);
		$datetime->setTimezone($la_time);
		$time = $datetime->format('m/d/Y h:i A');
	}


	$datetime = new DateTime($time, new DateTimeZone($timezone));
	$la_time = new DateTimeZone(SERVER_TIMEZONE);
	$datetime->setTimezone($la_time);
	$date = $datetime->format('m/d/Y h:i A');

	return $date;
}

function converFromServerTimeZone($time = "", $timezone)
{
	// $datetime = new DateTime($time);
	// $la_time = new DateTimeZone($timezone);
	// $datetime->setTimezone($la_time);
	// $date = $datetime->format('m/d/Y h:i A');
	// return $date; 

	// $datetime = new DateTime($time);
	// $la_time = new DateTimeZone(SERVER_TIMEZONE);
	// $datetime->setTimezone($la_time);
	// $date = $datetime->format('m/d/Y h:i A');

	$date = new DateTime($time, new DateTimeZone(SERVER_TIMEZONE));
	$date->setTimezone(new DateTimeZone($timezone));
	$time = $date->format('m/d/Y h:i A');
	return $time;
}

// function converFromServerTimeZone($time = "", $timezone)
// {
//     if (empty($time)) {
//         return "";
//     }

//     // Detect MM/DD/YYYY format and convert it to YYYY-MM-DD
//     if (preg_match('/^\d{2}\/\d{2}\/\d{4}$/', $time)) {
//         $time = DateTime::createFromFormat('m/d/Y', $time)->format('Y-m-d');
//     }

//     // Check if input is only a date without time
//     if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $time)) {
//         $time .= ' 23:59:59'; // Append time to prevent previous day shift
//     }

//     // Convert time from SERVER_TIMEZONE to target timezone
//     $date = new DateTime($time, new DateTimeZone(SERVER_TIMEZONE));
//     $date->setTimezone(new DateTimeZone($timezone));

//     return $date->format('m/d/Y h:i A');
// }


function GetBriefcaseDocumentPath($schoolId, $fileName)
{
	$defaultDocPath = '';
	if ($fileName != "") {
		$defaultDocPath = ROOT_PATH . "/upload/schools/" . $schoolId . "/Briefcase/" . $fileName;
		if (file_exists($defaultDocPath)) {
			$defaultDocPath =   BASE_PATH . "/upload/schools/" . $schoolId . "/Briefcase/" . $fileName;
		}
	}
	return $defaultDocPath;
}

function DeleteBriefcaseDocument($fileName)
{
	$deleteFolderPath = ROOT_PATH . "/upload/schools/" . $schoolId . "/Briefcase/" . $fileName;
	if (file_exists($deleteFolderPath))
		DeleteDirectory($deleteFolderPath);
}

function GetMasteryQuestionHtml($questionId, $type, $studentMasteryEvalId, $schoolId, $schoolmasteryQuestionTitle)
{
	$schoolmasteryQuestionTitle = $schoolmasteryQuestionTitle;

	$getTextAns = '';
	$SectionsId = '';

	$objQuestionOption = new clsQuestionOption();
	// For type MCQ
	$getQuestionOptions = $objQuestionOption->GetMasteryQuestionOptions($questionId, $studentMasteryEvalId);

	// Get section name
	$objMasteryEval = new clsMasteryEval();
	$SectionsId = $objMasteryEval->GetMasterySectionsId($questionId, $schoolId);
	$masteryTitle = $objMasteryEval->GetMasterySectionsTitle($SectionsId, $schoolId);
	if ($masteryTitle != '')
		$masteryTitle = strtolower(preg_replace('~[^\pL\d]+~u', '_', $masteryTitle));

	// For type Textarea
	$getTextAns = $objQuestionOption->GetTextAnsOfMasteryEval($questionId, $studentMasteryEvalId);

	unset($objQuestionOption);
	return RenderQuestionChoicesHtmlOfMastery($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId, $schoolmasteryQuestionTitle, $masteryTitle);
}


function RenderQuestionChoicesHtmlOfMastery($getQuestionOptions, $type, $questionId, $getTextAns, $ID, $schoolId, $schoolmasteryQuestionTitle, $masteryTitle)
{
	$qhtml = '';
	$totalTextCount = 0;

	//Display TextArea 
	if ($type == 2) {
		if ($getTextAns != '') {
			$totalTextCount = mysqli_num_rows($getTextAns);
		}

		if ($totalTextCount > 0) {
			while ($rows = mysqli_fetch_array($getTextAns)) {
				$TextAnswer = '';
				$TextAnswer = $rows["TextAnswer"];
				$qhtml .= '<p class="mt-1">Suggestions for improvement:</p><br/><textarea name="questionoptionst_' .  ($questionId) . '[]" id="textarea" class="form-control input-md clstextarea" rows="4" cols="100">' . $TextAnswer . '</textarea>';
			}
		} else {
			$qhtml .= '<p class="mt-1">Suggestions for improvement:<br/></p><textarea name="questionoptionst_' .  ($questionId) . '[]" id="textarea"  class="form-control input-md clstextarea" rows="4" cols="100"></textarea>';
		}

		$qhtml .= "<br/><table id='datatable-responsive' class='table table-bordered dt-responsive nowrap table-hover hide' cellspacing='0' width='100%'>
    
    		<thead>
    			<tr>
    				<th style='text-align:center'></th>
    				<th style='text-align:center'>Exceptional<br/>(Total of 40)</th>
    				<th style='text-align:center'>Proficient<br/>(Total of 34 — 39)</th>
    				<th style='text-align:center'>Remediated Proficiency<br/>(Total of 28 — 33)</th>
    				<th style='text-align:center'>Not Proficient<br/>(Total of 27 or less)</th>
    			</tr>
    		</thead>
    		<tbody>
    			<tr>
    				<td align='center'>Overall Score</td>
    				<td align='center'><input  class='form-control input-md' type='text' id='ExceptionalTotal' name='ExceptionalTotal'></td>
    				<td align='center'><input  class='form-control input-md' type='text' id='ProficientTotal' name='ProficientTotal'></td>
    				<td align='center'><input  class='form-control input-md' type='text' id='RemediatedTotal' name='RemediatedTotal'></td>
    				<td align='center'><input  class='form-control input-md' type='text' id='NotproficientTotal' name='NotproficientTotal'></td>
    			</tr>
    		</tbody>
    	</table>";
		//$qhtml .= "<ul class='nav navbar-nav list-inline'><li></br><b>Overall Score</b></li><li></br><b>Exceptional</b><br/>(Total of 40)</li><li></br><b>Proficient</b><br/>(Total of 34 — 39)</li><li></br><b>Remediated Proficiency</b><br/>(Total of 28 — 33)</li><li></br><b>Not Proficient</b><br/>(Total of 27 or less)</li></ul>";
	} else {
		$totalCount = 0;

		if ($getQuestionOptions != '') {
			$totalCount = mysqli_num_rows($getQuestionOptions);
		}
		$qhtml .= "<ul class='nav navbar-nav list-inline'><li></br><b>{$schoolmasteryQuestionTitle} </b></li>";
		if ($totalCount > 0) {
			$submittedChoiceArray = array();

			while ($row = mysqli_fetch_array($getQuestionOptions)) {

				if ($row["SelectedOptionValue"])
					$submittedChoiceArray[] = $row["SelectedOptionValue"];


				$QuestionOptions = $row["optionText"];
				$OptionValue = $row["OptionValue"];
				$description = $row["schoolOptionValue"];
				$selectedOption = $row['SelectedOptionValue'];
				$explodeQuestionOption = explode("-", $QuestionOptions);
				$explodeQuestionOptionCount = count($explodeQuestionOption);

				$masteryTen = '';

				if ($QuestionOptions == '10') {
					$masteryTen = 'exceptional';
				} elseif ($QuestionOptions == '8.5') {
					$masteryTen = 'proficient';
				} elseif ($QuestionOptions == '7') {
					$masteryTen = 'remediated ';
				} elseif ($QuestionOptions == '0.5') {
					$masteryTen = 'notproficient';
				}


				$strSeleted = '';

				if ($type == 1) {

					$strSeleted = "value='{$OptionValue}'";

					if (count($submittedChoiceArray) && in_array($selectedOption, $submittedChoiceArray)) {

						//for edit

						$strSeleted = "value='{$selectedOption}' checked='checked'";
					}

					$qhtml .= "<li><label class='radio-inline '><input type='radio' id='radio' class='radio_{$masteryTitle}_{$masteryTen} ExceptionalTotals' name='questionoptions_{$questionId}[]' {$strSeleted} sectionid=radio_{$masteryTitle}_{$masteryTen}><b>{$QuestionOptions}</b><b> Points</b></label>
    					<input type='hidden' name='hiddenRadioQuestion' value='{$questionId}' ><input type='hidden'  name='hiddenRadioSection' value='radio_{$masteryTitle}' ></br>{$description}</li>";
				}
			}
		}

		$qhtml .= "</ul>";
	}


	return $qhtml;
}

// function sendSMS($mobileNum,$body)
// {
// 	$mobileNum = str_replace("-","",$mobileNum);
// 	// $mobileNum = '+************';
// 	// echo $mobileNum;exit;
// 	$client = new Client(ACCOUNT_SID, AUTH_TOKEN_ID);  
// 	$message = $client->messages->create(			
// 		$mobileNum,
// 		array(
// 			'from' => REGISTER_NUMBER,							
// 			'Body' => $body
// 		));
// 	// print($message);exit;	
// }


// Using Telesign 
// function sendSMS($mobileNum, $message)
// {
// 	$responseMessage = '';
// 	$logFile = realpath(__DIR__ . '/../') . "/logs/sms.log";

// 	// $mobileNum = '+************';
// 	$mobileNum = str_replace("-", "", $mobileNum);
// 	$message = $message . ' Reply STOP to unsubscribe.';
// 	try {
// 		// Ensure log directory exists
// 		if (!file_exists(dirname($logFile))) {
// 			mkdir(dirname($logFile), 0775, true); // create logs/ if not exists
// 		}

// 		// Ensure log file exists
// 		if (!file_exists($logFile)) {
// 			file_put_contents($logFile, ""); // create empty file
// 		}

// 		if ($mobileNum != '+***********') {
// 			$customerId = TS_CUSTOMER_ID;
// 			$apiKey = TS_API_KEY;
// 			$messageType = "ARN";
// 			if (!$customerId || !$apiKey) {
// 				throw new Exception("Telesign credentials not found.");
// 			}

// 			$url = "https://rest-api.telesign.com/v1/messaging";
// 			$auth = base64_encode("$customerId:$apiKey");

// 			$postFields = http_build_query([
// 				'phone_number' => $mobileNum,
// 				'message' => $message,
// 				'message_type' => $messageType
// 			]);

// 			$ch = curl_init($url);
// 			curl_setopt($ch, CURLOPT_HTTPHEADER, [
// 				"Authorization: Basic $auth",
// 				"Content-Type: application/x-www-form-urlencoded"
// 			]);
// 			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
// 			curl_setopt($ch, CURLOPT_POST, true);
// 			curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);

// 			$response = curl_exec($ch);
// 			$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

// 			if (curl_errno($ch)) {
// 				$error = curl_error($ch);
// 				curl_close($ch);
// 				throw new Exception("cURL Error: $error");
// 			}

// 			curl_close($ch);

// 			if ($httpCode >= 200 && $httpCode < 300) {
// 				$responseMessage = "Message sent successfully.";
// 			} else {
// 				// $responseMessage = "Failed to send message. Response: $response";
// 				throw new Exception($response);

// 				error_log("Telesign SMS failed (HTTP $httpCode): $response");
// 			}
// 		}
// 	} catch (Exception $e) {
// 		// $timestamp = date("[Y-m-d H:i:s]");
// 		// // $responseMessage = "Error sending SMS to $mobileNum: " . $e->getMessage();
// 		// $logEntry = "$timestamp ERROR: Mobile: $mobileNum | Message: \"$message\" | Error: " . $e->getMessage() . "\n";
// 		// error_log($logEntry, 3, $logFile);
// 	}

// 	return $responseMessage;
// }

// Using ClickSend
/**
 * Sends an SMS message using the ClickSend API service
 *
 * @param string $mobileNum The mobile phone number to send SMS to
 * @param string $message The text message content to send
 * @return void
 */

 function sendSMS($mobileNum, $message)
{
	// Sanitize phone number: Remove any dashes from the mobile number
	$mobileNum = str_replace("-", "", $mobileNum);

	// Normalize phone number format: Add US country code (+1) if not already present
	if (strpos($mobileNum, '+') !== 0) {
		$mobileNum = '+1' . $mobileNum;
	}

	// Security check: Skip sending SMS to specific test/blocked number
	// This prevents accidental SMS charges to a particular number
	if ($mobileNum != '+***********') {

		// Configure ClickSend API credentials
		// Note: Consider moving these credentials to environment variables for security
		$config = ClickSend\Configuration::getDefaultConfiguration()
			->setUsername(COLORADO_USERNAME)
			->setPassword(COLORADO_LONG);

		// Initialize the ClickSend SMS API client with HTTP client and configuration
		$apiInstance = new ClickSend\Api\SMSApi(new GuzzleHttp\Client(), $config);

		// Create a new SMS message object and set its properties
		$msg = new \ClickSend\Model\SmsMessage();
		$msg->setBody($message);                    // Set the message content
		$msg->setTo($mobileNum);                   // Set the recipient phone number (with country code)
		$msg->setSource("PHP");                    // Set the source identifier for tracking

		// Note: The following commented lines are example usage for reference
		// $msg->setBody('Hello from ClickSend!');
		// $msg->setTo('+5926881947');   // Include country code

		// Create a message collection to hold the SMS message(s)
		// ClickSend API supports sending multiple messages in one request
		$collection = new \ClickSend\Model\SmsMessageCollection();
		$collection->setMessages([$msg]);

		// Attempt to send the SMS message via ClickSend API
		try {
			$result = $apiInstance->smsSendPost($collection);
			// Output the API response for debugging/logging purposes
			// Consider replacing with proper logging in production
			// print_r($result);
		} catch (Exception $e) {
			// Handle and display any errors that occur during SMS sending
			// Consider implementing proper error logging instead of echo
			// echo "Error sending SMS: ", $e->getMessage(), "\n";
		}
	}
}


// Create Short/tiny Url
function getTinyUrl($URL, $isMobile = 0)
{
	$data = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
	$randomNumber = substr(str_shuffle($data), 0, 8);
	$objDB = new clsDB();
	$isRandomNumber = $objDB->GetSingleColumnValueFromTable('redirecturls', 'redirectUrlId', 'redirectUrlId', $randomNumber);
	unset($objDB);
	if ($isRandomNumber != '') {
		getTinyUrl($URL);
	} else {
		if ($isMobile)
			include_once('../../class/clsDaily.php');
		else
			include_once('../class/clsDaily.php');
		$objDailyEval = new clsDaily();
		$redirectUrlId = $objDailyEval->SaveRedirectUrl($randomNumber, $URL);
		unset($objDailyEval);
	}
	return $randomNumber;
}

// Mask Mobile Number
function maskNumber($mobileNum)
{
	$formatted_number = '';
	if ($mobileNum != '') {
		$mobileNum = str_replace("-", "", $mobileNum);
		$masked_number = substr_replace($mobileNum, "XXXXXX", 0, 6);
		$formatted_number = substr($masked_number, 0, 3) . "-" . substr($masked_number, 3, 3) . "-" . substr($mobileNum, 6);
	}
	return $formatted_number;
}


/**
 * get access token from header
 * */
function getBearerToken()
{

	$headers = getAuthorizationHeader();

	// HEADER: Get the access token from the header
	if (!empty($headers)) {
		if (preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
			return $matches[1];
		}
	}
	return null;
}

/** 
 * Get header Authorization
 * */
function getAuthorizationHeader()
{
	$headers = null;
	// print_r($_SERVER);exit;
	if (isset($_SERVER['Authorization'])) {
		$headers = trim($_SERVER["Authorization"]);
	} else if (isset($_SERVER['HTTP_AUTHORIZATION'])) { //Nginx or fast CGI
		$headers = trim($_SERVER["HTTP_AUTHORIZATION"]);
	} elseif (function_exists('apache_request_headers')) {
		$requestHeaders = apache_request_headers();
		// Server-side fix for bug in old Android versions (a nice side-effect of this fix means we don't care about capitalization for Authorization)
		$requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));
		//print_r($requestHeaders);
		if (isset($requestHeaders['Authorization'])) {
			$headers = trim($requestHeaders['Authorization']);
		}
	}
	return $headers;
}

function CreateResponce($code, $success, $message, $result = "", $pager = "")
{
	header('Content-type: application/json');
	$payload = new stdClass();
	if ($result != "")
		$payload->data = $result;
	if ($pager != "")
		$payload->pager = $pager;

	$responce = array(
		"status" => $code,
		"success" => $success,
		"message" => $message,
		"payload" => $payload
	);
	echo json_encode(($responce));
	exit();
}

function getTimeZoneFromDate($date)
{
	$date = new DateTime($date);
	$timeZone = $date->getTimezone();
	$timeZoneName = $timeZone->getName();

	return $timeZoneName;
}

function compareVersions($appVersion)
{
	$targetIOSVersion = 'IOS.9.1.3.3';
	$targetAndroidVersion = 'AND.2.1.0';

	list($platformApp, $numbersApp) = explode('.', $appVersion, 2);

	if (strpos($platformApp, 'IOS') === 0) {
		$targetVersion = $targetIOSVersion;
	} elseif (strpos($platformApp, 'AND') === 0) {
		$targetVersion = $targetAndroidVersion;
	} else {
		return false; // Invalid platform
	}

	list($platformTarget, $numbersTarget) = explode('.', $targetVersion, 2);

	if ($platformApp != $platformTarget) {
		// Versions are for different platforms
		return false;
	}

	return version_compare($numbersApp, $numbersTarget, '>=');
}

function checkRotationStatus($rotationId, $isMobile = 0)
{
	$status = 0;
	// 	if ($isMobile)
	// 			include_once('../../class/clsRotation.php');
	// 		else
	// 			include_once('../class/clsRotation.php');
	if ($rotationId) {
		$objRotation	= new clsRotation();
		$rotationDetails = $objRotation->GetRotationDetailsByRotationId($rotationId);
		if ($rotationDetails != '') {
			$parentRotationId = $rotationDetails['parentRotationId'];
			$isSchedule = $rotationDetails['isSchedule'];
			if ($parentRotationId && $isSchedule) {
				$objDB = new clsDB();
				$rotationDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentRotationId);
				unset($objDB);
			} else {
				$rotationDate = $rotationDetails['endDate'];
			}
		}

		$givenDate = strtotime(date("Y-m-d", strtotime($rotationDate)));
		$currentDate = strtotime(date('Y-m-d'));

		if ($givenDate < $currentDate) {
			$status = 1;
		}
	}
	return $status;
	unset($objRotation);
}

function GetStudentNameForSms($studentId, $isMobile = 0)
{
	$studentfullname = '';

	if ($isMobile)
		include_once('../../class/clsStudent.php');
	else
		include_once('../class/clsStudent.php');

	if ($studentId) {
		$objStudent = new clsStudent();
		$studentfullname = $objStudent->GetStudentNameById($studentId);
		unset($objStudent);
	}
	return $studentfullname;
}

function getStudentName($tableName, $columnName, $whereColumnName, $whereColumnValue, $isMobile = 0)
{

	// Get the column value based on dynamic inputs
	$objDB = new clsDB();
	$columnValue = $objDB->GetSingleColumnValueFromTable($tableName, $columnName, $whereColumnName, $whereColumnValue);
	unset($objDB);

	// Get Student Name
	$studentNameForSms = GetStudentNameForSms($columnValue, $isMobile);

	// Format the student name
	$studentNameForSms = ($studentNameForSms != '') ? '( ' . $studentNameForSms . ' )' : '';

	return $studentNameForSms;
}

function GetAdminCIEvaluationQuestionHtml($questionId, $type, $ciEvaluationMasterId, $schoolId)
{
	$objQuestionOption = new clsQuestionOption();
	$getQuestionOptions = $objQuestionOption->GetQuestionOptionsOfAdminCIEvaluation($questionId, $ciEvaluationMasterId);
	$getTextAns = $objQuestionOption->GetTextAnsOfAdminCIEvaluation($questionId, $ciEvaluationMasterId);
	unset($objQuestionOption);
	return RenderQuestionChoicesHtml($getQuestionOptions, $type, $questionId, $getTextAns, 0, $schoolId);
}

function calculateNextDeliverDates($date, $repeatOnId, $isNextWeek = false)
{
	// Ensure $date is a DateTime object
	$date = new DateTime($date);

	$dayOfWeek = $date->format('N');

	if ($isNextWeek) {
		$startOfWeek = clone $date;
		$startOfWeek->modify('next Monday');
		$endOfWeek = clone $startOfWeek;
		$endOfWeek->modify('next Sunday');
	} else {
		$startOfWeek = clone $date;
		$startOfWeek->modify('-' . ($dayOfWeek - 1) . ' days');
		$endOfWeek = clone $startOfWeek;
		$endOfWeek->modify('+6 days');
	}

	$nextDeliverDate = clone $endOfWeek;
	if ($repeatOnId == 2) {
		$nextDeliverDate->modify('+1 day');
	}

	return [
		'startDate' => $startOfWeek,
		'endDate' => $endOfWeek,
		'nextDeliverDate' => $nextDeliverDate
	];
}

function calculateNextMonthlyDeliverDates($date, $repeatOnId, $isNextMonth = false)
{
	// Ensure $date is a DateTime object
	$date = new DateTime($date);

	if ($isNextMonth) {
		$startOfMonth = new DateTime($date->format('Y-m-01'));
		$startOfMonth->modify('first day of next month');
	} else {
		$startOfMonth = new DateTime($date->format('Y-m-01'));
	}
	$endOfMonth = clone $startOfMonth;
	$endOfMonth->modify('last day of this month');

	$nextDeliverDate = clone $endOfMonth;
	if ($repeatOnId == 4) {
		$nextDeliverDate->modify('+1 day');
	}

	return [
		'startDate' => $startOfMonth,
		'endDate' => $endOfMonth,
		'nextDeliverDate' => $nextDeliverDate
	];
}

function calculateNextQuarterlyDeliverDates($date, $repeatOnId, $isNextQuarter = false)
{
	// Ensure $date is a DateTime object
	$date = new DateTime($date);

	$currentMonth = $date->format('n');
	$currentYear = $date->format('Y');
	$startMonth = 1 + 3 * floor(($currentMonth - 1) / 3);

	if ($isNextQuarter) {
		$startMonth += 3;
		if ($startMonth > 12) {
			$startMonth = 1;
			$currentYear++;
		}
	}

	$startOfQuarter = new DateTime("{$currentYear}-{$startMonth}-01");
	$endOfQuarter = clone $startOfQuarter;
	$endOfQuarter->modify('last day of +2 months');

	$nextDeliverDate = clone $endOfQuarter;
	if ($repeatOnId == 6) {
		$nextDeliverDate->modify('+1 day');
	}

	return [
		'startDate' => $startOfQuarter,
		'endDate' => $endOfQuarter,
		'nextDeliverDate' => $nextDeliverDate
	];
}

function calculateNextYearlyDeliverDates($date, $repeatOnId, $isNextYear = false)
{
	// Ensure $date is a DateTime object
	$date = new DateTime($date);

	$currentYear = $date->format('Y');

	if ($isNextYear) {
		$currentYear++;
	}

	$startOfYear = new DateTime("{$currentYear}-01-01");
	$endOfYear = new DateTime("{$currentYear}-12-31");

	$nextDeliverDate = clone $endOfYear;
	if ($repeatOnId == 8) {
		$nextDeliverDate->modify('+1 day');
	}

	return [
		'startDate' => $startOfYear,
		'endDate' => $endOfYear,
		'nextDeliverDate' => $nextDeliverDate
	];
}

/**
 * Returns the client's IP address, taking into account cases where the
 * request may have been passed through a proxy.
 *
 * @return string The client's IP address.
 */
function getClientIP()
{
	if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
		// Check for IP from shared internet
		$ip = $_SERVER['HTTP_CLIENT_IP'];
	} elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
		// Check for IP passed from a proxy
		$ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
	} else {
		// Get the IP address from the remote address
		$ip = $_SERVER['REMOTE_ADDR'];
	}
	return $ip;
}

/**
 * Fetches the result set from a MySQL query as an array of associative arrays.
 *
 * This function iterates over a given MySQL result set, converting each row 
 * into an associative array and appending it to an output array. The keys 
 * in each associative array correspond to the column names in the result set.
 *
 * @param mysqli_result $result The result set from a MySQL query.
 * @return array The result set as an array of associative arrays, where each 
 *               element represents a row from the result set.
 */
function fetchResultAsArray($result)
{
	$jsonArray = [];
	while ($row = mysqli_fetch_assoc($result)) {
		$jsonArray[] = $row; // Add each row as an associative array
	}
	return $jsonArray;
}

/**
 * Generates an INSERT query from an array of rows.
 *
 * @param string $table The name of the table to insert into.
 * @param array $rows An array of associative arrays, where each sub-array
 *                    represents a row to insert with its column names as keys.
 *
 * @return string The generated INSERT query as a string.
 */
function generateInsertQueryFromArray($table, $rows)
{
	if (empty($rows)) {
		return ''; // No rows, return an empty string
	}

	// Get column names from the first row
	$columns = implode(", ", array_keys($rows[0]));

	// Initialize an array to hold each row's values
	$valuesArray = array_map(function ($row) {
		return "(" . implode(", ", array_map(function ($value) {
			return "'" . addslashes($value) . "'"; // Wrap each value in quotes and escape
		}, $row)) . ")";
	}, $rows);

	// Combine the rows' values into the VALUES section
	$values = implode(", ", $valuesArray);

	// Create the full INSERT query
	return "INSERT INTO $table ($columns) VALUES $values;";
}



/**
 * Extracts specified fields from the given row data.
 *
 * This function takes an associative array of row data and extracts the fields
 * specified in the $fields parameter. If a field is not present in the row data,
 * a default value from the $fields array is used. If no fields are specified,
 * the entire row data is returned.
 *
 * @param array $rowData An associative array representing a row of data.
 * @param array $fields An associative array where keys are the field names to extract
 *                      and values are the default values for missing fields.
 * @return array An associative array containing the extracted fields and their values.
 */
function extractFieldsForLogData($rowData, $fields = [])
{
	// If $fields is empty, return all data from $rowData
	if (empty($fields)) {
		return $rowData;
	}

	// Otherwise, extract only the specified fields
	$extractedData = [];
	foreach ($fields as $key => $defaultValue) {
		$extractedData[$key] = isset($rowData[$key]) ? $rowData[$key] : $defaultValue;
	}

	return $extractedData;
}

/**
 * Returns an associative array with the logged in user's ID and name.
 *
 * The returned array will contain two keys: 'userId' and 'userName'. The value
 * for 'userId' will be the user's ID in the database, and the value for
 * 'userName' will be the user's name, derived from the user's first and last
 * names.
 *
 * @return array An associative array with the user's ID and name.
 */
function getLoggedInUserDetails()
{
	// @session_start();
	$userId = isset($_SESSION['loggedUserId']) ? $_SESSION['loggedUserId'] : 0;
	$loggedUserFirstName = isset($_SESSION['loggedUserFirstName']) ? trim($_SESSION['loggedUserFirstName']) : '';
	$loggedUserLastName = isset($_SESSION['loggedUserLastName']) ? trim($_SESSION['loggedUserLastName']) : '';
	$userName = ($loggedUserFirstName && $loggedUserLastName) ? $loggedUserFirstName . ' ' . $loggedUserLastName : '';
	return ['userId' => $userId, 'userName' => $userName];
}

/**
 * Retrieves user details based on given user ID and user type.
 *
 * @param int $userId Unique identifier for the user.
 * @param string $userType Type of the user (Student, Clinician, or Admin).
 *
 * @return array An associative array with keys 'userId' and 'userName'.
 */
function getUserDetails($userId, $userType = '')
{

	// Get the column value based on dynamic inputs
	$objDB = new clsDB();
	if ($userType == 'Student')
		$userDetails = $objDB->GetSelectedColumnsFromTable('student', ["studentId", "firstName", "lastName"], 'studentId', $userId);
	elseif ($userType == 'Clinician')
		$userDetails = $objDB->GetSelectedColumnsFromTable('clinician', ["clinicianId", "firstName", "lastName"], 'clinicianId', $userId);
	elseif ($userType == 'Admin' || $userType == 'Super Admin')
		$userDetails = $objDB->GetSelectedColumnsFromTable('systemusermaster', ["systemUserMasterId", "firstName", "lastName"], 'systemUserMasterId', $userId);
	elseif ($userType == 'Preceptor')
		$userDetails = $objDB->GetSelectedColumnsFromTable('extenal_preceptors', ["id", "firstName", "lastName"], 'id', $userId);
	// Get Student Name
	// $userId = isset($studentDetails['studentId']) ? $studentDetails['studentId'] : 0;
	$firstName = isset($userDetails['firstName']) ? trim($userDetails['firstName']) : '';
	$lastName = isset($userDetails['lastName']) ? trim($userDetails['lastName']) : '';
	$userName = ($firstName && $lastName) ? $firstName . ' ' . $lastName : '';
	return ['userId' => $userId, 'userName' => $userName];
	unset($objDB);
}
// For all Date validations
function isValidDate($date)
{
	$invalidDates = [
		'',
		'0000-00-00 00:00:00',
		'11/30/-0001 01:00 AM',
		'11/30/-0001 12:00 AM',
		'11/29/-0001 10:00 PM'
	];

	// Check if the date is in the invalid list
	if (in_array($date, $invalidDates, true)) {
		return false;
	}

	// Check if the date is a valid format and not a placeholder date
	$timestamp = strtotime($date);
	if ($timestamp === false || $timestamp <= 0) {
		return false;
	}

	// Optional: Ensure the year is within a reasonable range (e.g., 1900–2100)
	$year = date('Y', $timestamp);
	if ($year < 1900 || $year > 2100) {
		return false;
	}

	return true;
}

//  super Admin Clinician & Student Searches
function formatPhoneNumberForSearch($input)
{
	// Check if the input contains only numbers or numbers with dashes
	if (is_numeric($input) || preg_match('/^(\d{3})-(\d{3})-(\d{4})$/', $input)) {
		// If input is a 10-digit number, format it as XXX-XXX-XXXX
		if (preg_match('/^\d{10}$/', $input)) {
			return preg_replace('/(\d{3})(\d{3})(\d{4})/', '$1-$2-$3', $input);
		}

		// If input is already formatted as XXX-XXX-XXXX, remove the dashes
		if (preg_match('/^(\d{3})-(\d{3})-(\d{4})$/', $input)) {
			return str_replace('-', '', $input);
		}
	}

	// If not a phone number, return input unchanged
	return $input;
}

// Function to Append Time If Missing
function appendTimeIfMissing($date)
{
	if (!empty($date)) {
		// If the date is in MM/DD/YYYY format, convert to YYYY-MM-DD
		if (preg_match('/^\d{2}\/\d{2}\/\d{4}$/', $date)) {
			$date = DateTime::createFromFormat('m/d/Y', $date)->format('Y-m-d');
		}

		// Check if input is only a date without time
		if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
			$date .= ' 23:59:59'; // Append time before passing to GetDateStringInServerFormat
		}
	}
	return $date;
}

// Auto Save Draft files function
function saveDraftFile($uploaddir, $userId, $hiddendraftId, $draftLimit,$data) {
    $draftId = 1;
    $draftFile = $uploaddir . "user_{$userId}_draft_{$draftId}.json";
    $currentDateTime = date('Y-m-d H:i:s');
    $createdDateTime = $currentDateTime;

    // If updating an existing draft (by id or hiddendraftId)
    if ((isset($_POST['draftId']) && is_numeric($_POST['draftId'])) || ($hiddendraftId > 0)) {
        $draftId = $hiddendraftId > 0 ? (int)$hiddendraftId : (int)$_POST['draftId'];
        $draftFile = $uploaddir . "user_{$userId}_draft_{$draftId}.json";
        if (file_exists($draftFile)) {
            $existingData = json_decode(file_get_contents($draftFile), true);
            if (isset($existingData['createdDateTime']) && strtotime($existingData['createdDateTime']) < strtotime($currentDateTime)) {
                $createdDateTime = $existingData['createdDateTime'];
            }
        }
    } else {
        // Auto-increment: find the highest existing draftId and add 1
        $draftId = 1;
        $files = glob($uploaddir . "user_{$userId}_draft_*.json");
        if ($files) {
            $maxId = 0;
            foreach ($files as $file) {
                if (preg_match('/user_' . $userId . '_draft_(\d+)\.json$/', $file, $matches)) {
                    $id = (int)$matches[1];
                    if ($id > $maxId) $maxId = $id;
                }
            }
            $draftId = $maxId + 1;
        }
        $draftFile = $uploaddir . "user_{$userId}_draft_{$draftId}.json";
    }

    $data['draftId'] = $draftId;
    $data['createdDateTime'] = $createdDateTime;
    $data['updatedDateTime'] = $currentDateTime;
	// print_r($data);   
	// exit;
    file_put_contents($draftFile, json_encode($data, JSON_PRETTY_PRINT));
    return ['message' => "Auto-saved at " . date("H:i:s"), 'draftId' => $draftId];
}
