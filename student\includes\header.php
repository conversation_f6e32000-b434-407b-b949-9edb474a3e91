<?php
@session_start();
// echo "<pre>";
// print_r(($_SESSION));
$randormRefreshId = rand(1, 100);
$currentstudentId = $_SESSION["loggedStudentId"];
$sessionEmail = '';
$loggedAsStudentBackUserId = isset($_SESSION['loggedAsStudentBackUserId']) ? $_SESSION['loggedAsStudentBackUserId'] : 0;
$sessionEmail = $sessionEmail ? ($_SESSION["loggedStudentEmail"]) : 'Logged As Student';
$isActiveCheckoff = '';
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];

//Show Server Time
$loggedStudentSchoolTimeZone = $_SESSION["loggedStudentSchoolTimeZone"];
$serverDateTime = date('Y-m-d H:i:s');
/* 30042021 */
$datetime = new DateTime($serverDateTime);
$la_time = new DateTimeZone($loggedStudentSchoolTimeZone);
$datetime->setTimezone($la_time);
$schoolLocalDate = $datetime->format('l d F, Y');
$schoolLocalTime = $datetime->format('h:i A');

$schoolLocalDateTime = $schoolLocalDate . ' Time is: ' . $schoolLocalTime;

// if($currentstudentId == '308'){
//     $serverDateTime = date('Y-m-d H:i:s',strtotime("+2 days"));
//     $datetime = new DateTime($serverDateTime);
// $la_time = new DateTimeZone($loggedStudentSchoolTimeZone);
// $datetime->setTimezone($la_time);
// $schoolLocalDate = $datetime->format('l d F, Y');
// $schoolLocalTime = $datetime->format('h:i A');
//     $schoolLocalDateTime = $schoolLocalDate . ' Time is: ' . $schoolLocalTime;
// }
/* 30042021 */

//   $schoolLocalDateTime = converFromServerTimeZone($serverDateTime,$loggedStudentSchoolTimeZone);
//   $schoolLocalDate = date('l d F, Y',strtotime($schoolLocalDateTime));
//   $schoolLocalTime = date('h:i A',strtotime($schoolLocalDateTime));
//   $schoolLocalDateTime = $schoolLocalDate.' Time is: '.$schoolLocalTime;

$caseStudyIconPath = BASE_PATH . '/assets/images/casestudy.png';

$objDB = new clsDB();
$isActivitySheet = $objDB->GetSingleColumnValueFromTable('schools', 'activitySheet', 'schoolId', $currentSchoolId);
$studentEmail = $objDB->GetSingleColumnValueFromTable('student', 'email', 'studentId', $currentstudentId);
$isChat = $objDB->GetSingleColumnValueFromTable('schoolsettings', 'status', 'schoolId', $currentSchoolId, 'type', 'chat');
$isAutoSave = $objDB->GetSingleColumnValueFromTable('schoolsettings', 'status', 'schoolId', $currentSchoolId, 'type', 'autoSave');
$draftLimit = $objDB->GetSingleColumnValueFromTable('schoolsettings', 'moduleLimit', 'schoolId', $currentSchoolId, 'type', 'autoSave');


?>

<style>
 .dropdown-divider {
    height: 0;
    margin: 0.5rem 0;
    overflow: hidden;
    border-top: 1px solid #e9ecef;
  }

  .bgColour {
    background-color: 'white';
  }

  /* Popover Styling */
  .custom-popover {
    display: none;
    position: absolute;
    top: 50px;
    right: 10px;
    width: 450px;
    background: white;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    /* border-top-right-radius: 0; */
    z-index: 1000;
    overflow: hidden;
  }

  .popover-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
    font-size: 15px;
    font-weight: 600;
  }

  .popover-tabs {
    display: flex;
    justify-content: space-around;
    padding: 10px;
    border-bottom: 1px solid #ddd;
  }

  .popover-tabs button {
    background: none;
    border: none;
    font-size: 14px;
    color: #555;
    cursor: pointer;
    display: flex;
    align-items: center;
  }

  .popover-tabs button.active {
    font-weight: 500;
    border-bottom: 2px solid #01A750;
  }

  .popover-content {
    max-height: 500px;
    overflow-y: auto;
    padding: 10px;

     /* Customize scrollbar width and color */
     scrollbar-width: thin; /* For Firefox: sets the width of the scrollbar */
    scrollbar-color: #c1c1c1 #ecf0f1; /* For Firefox: sets the thumb and track colors (thumb, track) */

  }


/* For Webkit browsers (Chrome, Safari, Edge) */
.popover-content::-webkit-scrollbar {
    width: 2px; /* Width of the scrollbar */
}

.popover-content::-webkit-scrollbar-thumb {
    background-color: #01A750; /* Color of the thumb */
    border-radius: 10px; /* Rounded corners for the thumb */
}

.popover-content::-webkit-scrollbar-track {
    background-color: #ecf0f1; /* Color of the track */
}

  .notification-item {
    display: flex;
    align-items: start;
    flex-direction: column;
    padding: 10px;
    border-bottom: 1px solid #eee;
  }

  .notification-avatar {
    flex-shrink: 0;
    margin-right: 10px;
    width: 50px;
    height: 50px;
    border: 1px solid #01A750;
    padding: 2px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    overflow: hidden;
  }

  .notification-avatar img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 50%;
  }

  .notification-details{
    color: #000000;
    width: 100%;
  }

  .notification-details h4 {
    margin-bottom: 0;
    font-size: 16px;
    font-weight: 500;
  }

  .notification-details h6 {
    margin: 2px 0;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    font-weight: 400;
  }

  .notification-text{
    font-size: 10px;
  }

  .notification-list {
    list-style: none;
    padding: 0;
  }

  .viewNotificationPopup:hover{
    text-decoration: none;
  }

  .notification-info {
    flex-grow: 1;
  }

  .notification-info p {
    margin: 0;
    font-size: 14px;
  }

  .notification-info .time {
    font-size: 12px;
    color: gray;
    margin-top: 5px;
    display: block;
  }

  .actions button {
    margin-right: 5px;
    padding: 5px 10px;
    font-size: 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .actions .accept {
    background: #007bff;
    color: white;
  }

  .actions .deny {
    background: #ddd;
    color: black;
  }

  .file-info {
    font-size: 12px;
    color: gray;
    margin-top: 5px;
  }

  /* Tabs styling */
.popover-tabs {
    display: flex;
    justify-content: start;
    padding: 10px;
    border-bottom: 1px solid #ddd;
}

.tab-button {
    background: none;
    border: none;
    font-size: 14px;
    color: #555;
    cursor: pointer;
    padding: 5px 10px;
}

.tab-button.active {
    font-weight: bold;
    border-bottom: 2px solid #01A750;
    color: #01A750;
}

/* Tab content styling */
.tab-content {
    display: none; /* Hidden by default */
}

.tab-content.active {
    display: block; /* Show active tab content */
}

.count{
  font-size: 11px;
  padding: 0 6px;
    width: 22px;
    background: #abacaf;
    border-radius: 50%;
    color: #fff;
    margin-left: 5px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 0;
}

.time{
  text-align: start;
    font-size: 12px;
}

.notification-msg{
  margin-bottom: 0;
}

hr {
    margin-top: 5px;
    margin-bottom: 5px;
}

.popover-tabs button.active .count {
  background: #01A750; 
}
  #backdrop {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    /* Semi-transparent black */
    z-index: 999;
    /* Just below the popover (z-index: 1000) */
  }
    #top-bar-header .navbar-default .navbar-brand {
    padding: 10px;
    height: fit-content !important;
}
#top-bar-header .navbar-default .navbar-brand img {
    width: 100% !important;
    height: 70px !important;
}
</style>

<input type="text" name="draftLimit" id="draftLimit" value="<?php echo $draftLimit; ?>">

<div class="row margin_zero" style="background-color:#666666;">
  <div class="row margin_zero" id="top-bar-header">
    <nav class="navbar navbar-default">
      <div class="container">
        <!-- Brand and toggle get grouped for better mobile display -->
        <div class="navbar-header">
          <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-2" aria-expanded="false">
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
          <a class="navbar-brand" href="dashboard.html"><img class="img-responsive" src="<?php echo ($currenSchoolLogoImagePath) . '?rand=' . $randormRefreshId; ?>" alt=""></a>
        </div>

        <!-- Collect the nav links, forms, and other content for toggling -->
        <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-2">


          <ul class="nav navbar-nav navbar-right">
            <?php

            if (isset($_SESSION["loggedAsStudentBackUserId"])) { ?>
              <li><a style="font-size:12px;" href="../admin/loginasschooluser.html?userId=<?php echo (EncodeQueryData($_SESSION["loggedAsStudentBackUserId"])); ?>&type=backtoAdmin">Back To Admin</a></li>
              <?php
            }
            if (isset($_SESSION["loggedCheckPendingCkockOut"])) {
              if ($_SESSION["loggedCheckPendingCkockOut"] > 0) { ?>
                <!--li><a href="javascript:void(0);">Clock Me Out</a></li-->
            <?php   }
            } ?>
            <li><a href="https://www.clinicaltrac.com/student.html" data-toggle="tooltip" data-placement="bottom" title="Help" target="_blank"><i class="fa fa-question-circle-o ng-scope"></i></a></li>
            <!-- <li><a href="javascript:void(0);"><i class="fa fa-bell-o ng-scope"></i><span id="spanNotificationCount" class="badge badge-custom"></span></a></li>-->
            <li class="dropdown">
              <div>
                <!-- <div id="notification-header" style="text-align:left;">
                  <div>
                    <button id="notification-icon" style="background: transparent;margin-top:5px" name="button" class="dropbtn notification-icon" data-toggle="tooltip" data-placement="bottom" title="Notification"><i class="fa fa-bell-o ng-scope"></i></button>
                    <div id="notification-latest" class="notification-latest" style="text-align: left;max-width: 250px;color: white;position: absolute;box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.20);background: #17eccf;"></div>
                  </div>
                </div> -->

                <div id="notification-header" style="text-align: left;">
                    <div>
                      <button id="<?php if($isChat == 1 && $loggedAsStudentBackUserId == 0) {?>notification-icon1<?php } else { ?>notification-icon<?php }?>" type="button" style="background: transparent; margin-top: 5px;border: none;padding: 0;position: relative;" name="button" class="dropbtn notification-icon" title="Notification" onclick="GetChatNotificationsList(4, '<?php echo ($_SESSION['loggedStudentEmail']); ?>', '<?php echo EncodeQueryData($currentSchoolId); ?>', '<?php echo EncodeQueryData($_SESSION['loggedStudentId']); ?>');">
                        <i class="fa fa-bell-o ng-scope"></i>
                        <span id="notification-count" style='color:white' class="badge badge-custom"></span>
                      </button>
                      <!-- Popover -->
                      <div id="custom-popover" class="custom-popover" style="display: none;">
                        <div class="popover-header">
                          <span>Notifications</span>
                          <!-- <a href="#" class="mark-all-read">Mark all as read</a> -->
                        </div>
                        <div class="popover-tabs">
                          <button class="tab-button active" data-tab="chatApp" onclick="openTabChatNotification()">Chat <p class="countValue2 count" id="countValue2"></p></button>
                          <a href="studentnotification.html"><button class="tab-button " data-tab="webNoti">Notification <p class="count" id="webNotiCount"></p></button></a>
                        </div>
                        <div class="popover-content">
                          <!-- Tab content for "All" -->
                          <div class="tab-content" id="webNoti" style="display: none;">
                            <!-- <div class="notification-item">
                              <p class="notification-msg"><strong>Ralph Edwards</strong> wants to edit <strong>Tetrisly Design System</strong></p>
                              <span class="time">5 min ago</span>
                            </div>
                            <div class="notification-item">
                              <p class="notification-msg"><strong>Robert Fox</strong> added file to <strong>Dark mode</strong></p>
                              <span class="time">1 hour ago</span>
                            </div> -->
                          </div>
                          <!-- Tab content for "Following" -->
                          <div class="tab-content" id="chatApp" style="display: block;">
                            <p>No notifications in Chat.</p>
                          </div>
                        
                        </div>
                      </div>

                    </div>
                  </div>


                  <!-- Backdrop -->
                  <div id="backdrop" style="display: none;"></div>

                <?php if (isset($message)) { ?> <div class="error"><?php echo $message; ?></div> <?php } ?>


                <?php if (isset($success)) { ?> <div class="success"><?php echo $success; ?></div> <?php } ?>
              </div>
            </li>

            <!-- Chat Icon start -->
              <?php if($isChat == 1 && $loggedAsStudentBackUserId == 0) {?>
              <li>
              <a class="nav-link" id="authenticat_user" href="javascript:void(0);" data-toggle="tooltip" data-placement="bottom" title="Chat" onclick="chatAuth('0','0','<?php echo EncodeQueryData($currentSchoolId);?>','<?php echo EncodeQueryData($_SESSION['loggedStudentId']);?>','4','<?php echo ($_SESSION['loggedStudentEmail']); ?>')"><i class="fa fa-solid fa-comment"></i></a>
              </li>
              <?php } ?>
              <!-- Chat Icon end -->

            <li class="dropdown">
              <a href="javascript:void(0);" class="dropdown-toggle user-profile-photo" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                <img class="img-responsive user-image" src="<?php echo ($_SESSION["loggedStudentProfileImagePath"]); ?>?id=<?php echo ($randormRefreshId); ?>" alt="" style="border:none;">
                <span class="user-dropdown"><?php echo ($_SESSION["loggedStudentFirstName"] . ' ' . $_SESSION["loggedStudentLastName"]) ?> <span class="caret"></span></span></a>
              <ul class="dropdown-menu">
                <li><a href="editprofile.html">Edit Profile</a></li>
                <li><a href="changepassword.html">Change Password</a></li>
                <!-- <li><a href="faqquestion.html">Help</a></li>  -->
                <li><a href="mailto:<EMAIL>?subject=<?php //echo($currenschoolDisplayname); 
                                                                      ?> | Issue">Support</a></li>
                <div class="dropdown-divider"></div>
                <?php if($isChat == 1 && $loggedAsStudentBackUserId == 0) {?>
                  <li><a href="javascript:void(0); " id="logoutButton">Logout</a></li>
                  <?php } else { ?> 
                    <li><a href="logout.html">Logout</a></li>                
                <?php } ?>

                <div class="dropdown-divider"></div>
                <li><a class="active"><?php //echo ($sessionEmail); 
                                      ?></a></li>
              </ul>
            </li>
            <br>        
                    <li class="" style="    width: 100%;padding-right: 17px;">    
                   <div style="color:white;font-size:12px;width: 100%;text-align: right;"><?php echo ($schoolLocalDateTime); ?>           
                             </div>   
                        </li>
 
          </ul>
        </div><!-- /.navbar-collapse -->
      </div><!-- /.container-fluid -->
    </nav>
  </div>

  <!-- <div class="container">
    <div style="color:white;text-align:right;font-size:12px;padding-right:20px;"><?php echo ($schoolLocalDateTime); ?></div>
  </div> -->

  <div class="row margin_zero text-center" id="top-bar-header-menu">
    <nav class="navbar navbar-default">
      <div class="container-fluid">
        <!-- Brand and toggle get grouped for better mobile display -->
        <div class="navbar-header">
          <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
        </div>
        <!-- Collect the nav links, forms, and other content for toggling -->
        <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
          <ul class="nav navbar-nav">
            <li class=""><a href="dashboard.html"><i class="fa fa-th-list"></i>Dashboard</a></li>
            <?php //if($_SESSION["loggedClinicianRoleId"]==APPLICANTION_SUPERADMIN_ROLE_ID){ 
            ?>
            <!--<li><a href="courses.html"><i class="fa fa-company-icon"></i>Courses</a></li>-->
            <li><a href="rotations.html?active=1"><i class="fa fa-recycle"></i>Rotation</a></li>
            <li><a href="Attendance.html"><i class="fa fa-calendar"></i>Attendance</a></li>
            <li><a href="journal.html"><i class="fa fa-newspaper-o"></i>Journal</a></li>
            <li><a href="interaction.html"><i class="fa fa-address-book-o"></i>Dr.Interaction </a></li>
            <li><a href="procedurecounts.html"><i class="fa fa-newspaper-o"></i>Procedure Count </a></li>
            <?php if ($isActivitySheet == 1) { ?>
              <li><a href="activitysheetlist.html"><i class="fa fa-file-text-o" aria-hidden="true"></i>Activity Sheet </a></li>
            <?php } ?>
            <?php if ($isActiveCheckoff == 1) { ?>
              <li><a href="checkoff.html"><i class="fa fa-calendar-check-o"></i>Checkoff</a></li>
            <?php } else if ($isActiveCheckoff == 2) { ?>
              <li><a href="checkoffusaf.html"><i class="fa fa-calendar-check-o"></i>Checkoff</a></li>
            <?php } else { ?>
              <li><a href="checkoffs.html"><i class="fa fa-calendar-check-o"></i>Checkoff</a></li>
            <?php } ?>
            <li><a href="dailyEvalList.html"><i class="fa fa-calendar-check-o"></i>Daily/Weekly</a></li>
            <?php //if($isActiveCheckoff != 2) { 
            ?>
            <li><a href="caseStudyList.html"><img src="<?php echo $caseStudyIconPath; ?>" style="display: block;margin-bottom: 4px;">Case Study</a></li>
            <?php //} 
            ?>
            <li><a href="viewBriefcase.html"><i class="fa fa-briefcase"></i>Briefcase</a></li>
            <?php if($isChat == 1 && $loggedAsStudentBackUserId == 0) { ?>

            <!-- <li class="nav-item">
              <a class="nav-link" id="authenticat_user" href="javascript:void(0);" onclick="chatAuth('0','0','<?php echo EncodeQueryData($currentSchoolId);?>','<?php echo EncodeQueryData($currentstudentId);?>','4','<?php echo ($studentEmail); ?>')"><i class="fa fa-solid fa-comment"></i><span data-i18n="Vertical">Chat</span></a>
            </li> -->
            <?php } ?>
          </ul>
        </div><!-- /.navbar-collapse -->
      </div><!-- /.container-fluid -->
    </nav>
  </div>
</div>

<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script>
  $(document).ready(function() {
    $('#notification-icon1').click(function(event) {
      console.log("clicked")
      event.preventDefault();
      event.stopPropagation();

      // Toggle popover visibility and backdrop
      const popover = $('#custom-popover');
      const backdrop = $('#backdrop');

      if (popover.is(':visible')) {
        popover.hide();
        backdrop.hide();
      } else {
        popover.show();
        backdrop.show();
      }
    });

    // Hide popover and backdrop when clicking outside
    $(document).click(function() {
      $('#custom-popover').hide();
      $('#backdrop').hide();
    });

    // Prevent closing when clicking inside the popover
    $('#custom-popover').click(function(event) {
      event.stopPropagation();
    });

    // Prevent closing when clicking on the backdrop itself
    $('#backdrop').click(function() {
      $('#custom-popover').hide();
      $('#backdrop').hide();
    });
  });

  $(document).ready(function () {
    // Handle tab switching
    $('.tab-button').click(function () {
        const selectedTab = $(this).data('tab'); // Get the data-tab value of the clicked button

        // Remove 'active' class from all tabs and add it to the clicked tab
        $('.tab-button').removeClass('active');
        $(this).addClass('active');

        // Hide all tab contents and show the selected one
        $('.tab-content').removeClass('active').hide();
        $(`#${selectedTab}`).addClass('active').show();
    });
});

</script>